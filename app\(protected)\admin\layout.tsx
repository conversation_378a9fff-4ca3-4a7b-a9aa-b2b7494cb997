import { Metadata } from 'next'
import { ProtectedRoute } from '@/components/auth/protected-route'

export const metadata: Metadata = {
  title: {
    template: '%s | JobPortal Admin',
    default: 'Admin Dashboard | JobPortal',
  },
  description: 'Administrative dashboard for JobPortal.',
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ProtectedRoute requiredRole="admin">
      {children}
    </ProtectedRoute>
  )
}
