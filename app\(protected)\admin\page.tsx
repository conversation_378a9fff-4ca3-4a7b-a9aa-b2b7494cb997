'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { AdminDashboard } from '@/components/admin/admin-dashboard'
import { PageLoader } from '@/components/ui/page-loader'
import { ErrorAlert } from '@/components/ui/error-alert'

export default function AdminPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuthStore()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Check if user is admin
    if (isAuthenticated && user) {
      if (user.role !== 'admin') {
        router.push('/dashboard')
        return
      }
      setIsLoading(false)
    }
  }, [isAuthenticated, user, router])

  if (isLoading) {
    return <PageLoader />
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <ErrorAlert
          message={error}
          dismissible={true}
          onDismiss={() => setError(null)}
          actions={
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setError(null)
                setIsLoading(true)
              }}
              className="ml-2"
            >
              Retry
            </Button>
          }
        />
      </div>
    )
  }

  return (
    <ProtectedRoute requiredRole="admin">
      <div className="min-h-screen bg-background">
        <AdminDashboard />
      </div>
    </ProtectedRoute>
  )
}
