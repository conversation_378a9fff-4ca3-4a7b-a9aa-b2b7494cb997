import { Metadata } from 'next'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { SharedLayout } from '@/components/layouts/shared-layout'

export const metadata: Metadata = {
  title: {
    template: '%s | JobPortal Dashboard',
    default: 'Dashboard | JobPortal',
  },
  description: 'Manage your job search, applications, and profile on JobPortal.',
}

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ProtectedRoute>
      <SharedLayout>
        {children}
      </SharedLayout>
    </ProtectedRoute>
  )
}
