'use client'

import React, { useEffect, useState } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { useJobsStore, useAuthStore } from '@/stores'
import { JobApplicationForm } from '@/components/jobs/job-application-form'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { PageLoader } from '@/components/ui/page-loader'
import { ErrorAlert } from '@/components/ui/error-alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, CheckCircle, Building, MapPin, Clock } from 'lucide-react'

function JobApplicationContent() {
  const params = useParams()
  const router = useRouter()
  const jobId = params.id as string
  
  const { user } = useAuthStore()
  const { 
    currentJob,
    jobLoading,
    error,
    appliedJobs,
    getJobById,
    clearError
  } = useJobsStore()

  const [applicationSubmitted, setApplicationSubmitted] = useState(false)
  const isAlreadyApplied = appliedJobs.includes(jobId)

  // Load job details
  useEffect(() => {
    if (jobId) {
      getJobById(jobId)
    }
  }, [jobId, getJobById])

  // Handle successful application
  const handleApplicationSuccess = () => {
    setApplicationSubmitted(true)
  }

  // Handle cancel
  const handleCancel = () => {
    router.push(`/jobs/${jobId}`)
  }

  // Loading state
  if (jobLoading) {
    return <PageLoader message="Loading job details..." fullScreen />
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <ErrorAlert
            type="error"
            title="Failed to Load Job"
            message={error.message || 'Could not load job details. Please try again.'}
            dismissible
            onDismiss={clearError}
            actions={
              <div className="flex space-x-2 mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push('/jobs')}
                >
                  Browse Jobs
                </Button>
                <Button
                  size="sm"
                  onClick={() => getJobById(jobId)}
                >
                  Try Again
                </Button>
              </div>
            }
          />
        </div>
      </div>
    )
  }

  // Job not found
  if (!currentJob) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Job Not Found</h1>
          <p className="text-muted-foreground mb-4">
            The job you're trying to apply for doesn't exist or has been removed.
          </p>
          <Button onClick={() => router.push('/jobs')}>
            Browse All Jobs
          </Button>
        </div>
      </div>
    )
  }

  // Application submitted success state
  if (applicationSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <Card className="text-center">
              <CardHeader className="pb-4">
                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <CardTitle className="text-2xl">Application Submitted!</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <p className="text-lg">
                    Your application for <strong>{currentJob.title}</strong> at{' '}
                    <strong>{currentJob.company.name}</strong> has been successfully submitted.
                  </p>
                  <p className="text-muted-foreground">
                    You should receive a confirmation email shortly. The employer will review your 
                    application and contact you if you're selected for the next steps.
                  </p>
                </div>

                <div className="bg-muted/50 rounded-lg p-4 space-y-2">
                  <h4 className="font-medium">What happens next?</h4>
                  <ul className="text-sm text-muted-foreground space-y-1 text-left">
                    <li>• You'll receive an email confirmation</li>
                    <li>• The employer will review your application</li>
                    <li>• You'll be contacted if selected for an interview</li>
                    <li>• You can track your application status in your dashboard</li>
                  </ul>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    variant="outline"
                    onClick={() => router.push('/applications')}
                  >
                    View My Applications
                  </Button>
                  <Button
                    onClick={() => router.push('/jobs')}
                  >
                    Find More Jobs
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  // Already applied state
  if (isAlreadyApplied) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <Card className="text-center">
              <CardHeader>
                <CardTitle className="text-xl">Already Applied</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">
                  You have already submitted an application for this position.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    variant="outline"
                    onClick={() => router.push('/applications')}
                  >
                    View Application Status
                  </Button>
                  <Button
                    onClick={() => router.push('/jobs')}
                  >
                    Find More Jobs
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={() => router.push(`/jobs/${jobId}`)}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Job Details</span>
            </Button>
            
            <Badge variant="outline">
              Application in Progress
            </Badge>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <JobApplicationForm
          job={currentJob}
          onSuccess={handleApplicationSuccess}
          onCancel={handleCancel}
        />
      </main>
    </div>
  )
}

export default function JobApplicationPage() {
  return (
    <ProtectedRoute requiredRole="job_seeker">
      <JobApplicationContent />
    </ProtectedRoute>
  )
}
