'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { JobApplicationModal } from '@/components/jobs/job-application-modal'
import { getJobById } from '@/lib/job-data'
import { 
  MapPin, 
  DollarSign, 
  Clock, 
  Eye, 
  Heart, 
  Briefcase,
  CheckCircle,
  TrendingUp,
  Users,
  Calendar,
  ExternalLink,
  Bookmark,
  Share2,
  Building,
  Globe,
  Zap,
  Star,
  GraduationCap,
  Shield,
  Plane,
  Languages,
  Award,
  Target,
  ChevronRight,
  ArrowLeft,
  Send,
  Download,
  MessageSquare
} from 'lucide-react'

export default function PremiumJobDetailPage() {
  const params = useParams()
  const router = useRouter()
  const jobId = params.id as string
  
  const [activeTab, setActiveTab] = useState('overview')
  const [isSaved, setIsSaved] = useState(false)
  const [showApplicationModal, setShowApplicationModal] = useState(false)

  // Get job data from service - in real app this would come from API based on ID
  const job = getJobById(Number(jobId)) || {
    id: 1,
    title: "Job Not Found",
    company: {
      id: 1,
      name: "Unknown Company",
      logo: "/api/placeholder/80/80",
      industry: "Unknown",
      size: "Unknown",
      rating: 0,
      verified: false
    },
    location: "Unknown",
    type: "Full-time" as const,
    remote: false,
    salary: {
      min: 0,
      max: 0,
      currency: "USD",
      period: "year" as const
    },
    experience: "Entry Level" as const,
    description: "Job not found",
    requirements: [],
    responsibilities: [],
    benefits: [],
    skills: [],
    department: "Unknown",
    posted: "Unknown",
    applicants: 0,
    views: 0,
    featured: false,
    urgent: false,
    category: "Unknown",
    workModel: "On-site" as const
  }

  const formatSalary = (salary: typeof job.salary) => {
    const min = salary.min.toLocaleString()
    const max = salary.max.toLocaleString()
    const period = salary.period === 'year' ? '/year' : salary.period === 'month' ? '/month' : '/hour'
    return `$${min} - $${max}${period}`
  }

  const handleSave = () => {
    setIsSaved(!isSaved)
    console.log('Save job:', job.title)
  }

  const handleApply = () => {
    setShowApplicationModal(true)
  }

  return (
    <div className="pt-16 min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      {/* Header */}
      <section className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-6 max-w-6xl">
          <div className="flex items-center space-x-4 mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Jobs</span>
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <span>Jobs</span>
              <ChevronRight className="w-4 h-4" />
              <span>{job.category}</span>
              <ChevronRight className="w-4 h-4" />
              <span className="text-foreground">{job.title}</span>
            </div>
          </div>

          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
            {/* Job Info */}
            <div className="flex items-start space-x-6 flex-1">
              <div className="relative">
                <Avatar className="w-20 h-20 border-2 border-primary/20">
                  <AvatarImage src={job.company.logo} alt={job.company.name} />
                  <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-xl">
                    {job.company.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                  </AvatarFallback>
                </Avatar>
                {job.company.verified && (
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-white" />
                  </div>
                )}
              </div>
              
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-3">
                  <h1 className="text-3xl font-bold">{job.title}</h1>
                  {job.featured && (
                    <Badge variant="secondary" className="bg-yellow-500 text-white">
                      <Star className="w-4 h-4 mr-1" />
                      Featured
                    </Badge>
                  )}
                  {job.urgent && (
                    <Badge variant="secondary" className="bg-red-500 text-white">
                      <Zap className="w-4 h-4 mr-1" />
                      Urgent
                    </Badge>
                  )}
                </div>
                <p className="text-primary font-semibold text-xl mb-4">{job.company.name}</p>
                <div className="flex flex-wrap items-center gap-6 text-muted-foreground">
                  <div className="flex items-center space-x-2">
                    <MapPin className="w-5 h-5" />
                    <span className="font-medium">{job.location}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <DollarSign className="w-5 h-5" />
                    <span className="font-medium">{formatSalary(job.salary)}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Briefcase className="w-5 h-5" />
                    <span className="font-medium">{job.type}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Globe className="w-5 h-5" />
                    <span className="font-medium">{job.workModel}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-5 h-5" />
                    <span className="font-medium">{job.experience}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row lg:flex-col gap-3 lg:w-64">
              <Button onClick={handleApply} size="lg" className="button-premium">
                <Send className="w-5 h-5 mr-2" />
                Apply Now
              </Button>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="lg"
                  onClick={handleSave}
                  className={`flex-1 ${isSaved ? 'text-yellow-500 border-yellow-500' : ''}`}
                >
                  <Bookmark className={`w-5 h-5 mr-2 ${isSaved ? 'fill-current' : ''}`} />
                  {isSaved ? 'Saved' : 'Save'}
                </Button>
                <Button variant="outline" size="lg">
                  <Share2 className="w-5 h-5" />
                </Button>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-primary">{job.applicants}</div>
              <div className="text-sm text-muted-foreground">Applicants</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-primary">{job.views.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Views</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-primary">{job.posted}</div>
              <div className="text-sm text-muted-foreground">Posted</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-primary">{job.company.rating}</div>
              <div className="text-sm text-muted-foreground">Company Rating</div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="requirements">Requirements</TabsTrigger>
                <TabsTrigger value="company">Company</TabsTrigger>
                <TabsTrigger value="benefits">Benefits</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-8 mt-8">
                {/* Job Description */}
                <Card className="card-premium">
                  <CardHeader>
                    <CardTitle>Job Description</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed text-lg">{job.description}</p>
                  </CardContent>
                </Card>

                {/* Key Responsibilities */}
                <Card className="card-premium">
                  <CardHeader>
                    <CardTitle>Key Responsibilities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {job.responsibilities.map((responsibility, index) => (
                        <li key={index} className="flex items-start space-x-3">
                          <ChevronRight className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                          <span className="text-muted-foreground">{responsibility}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>

                {/* Skills */}
                <Card className="card-premium">
                  <CardHeader>
                    <CardTitle>Required Skills</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-3">
                      {job.skills.map((skill) => (
                        <Badge key={skill} variant="secondary" className="theme-glow text-sm py-2 px-3">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="requirements" className="space-y-8 mt-8">
                {/* Requirements */}
                <Card className="card-premium">
                  <CardHeader>
                    <CardTitle>Requirements</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {job.requirements.map((requirement, index) => (
                        <li key={index} className="flex items-start space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-muted-foreground">{requirement}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>

                {/* Additional Requirements */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {job.education && (
                    <Card className="card-premium">
                      <CardContent className="p-6">
                        <div className="flex items-start space-x-3">
                          <GraduationCap className="w-6 h-6 text-primary mt-0.5" />
                          <div>
                            <h4 className="font-semibold text-lg mb-2">Education</h4>
                            <p className="text-muted-foreground">{job.education}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                  
                  {job.languages && (
                    <Card className="card-premium">
                      <CardContent className="p-6">
                        <div className="flex items-start space-x-3">
                          <Languages className="w-6 h-6 text-primary mt-0.5" />
                          <div>
                            <h4 className="font-semibold text-lg mb-2">Languages</h4>
                            <p className="text-muted-foreground">{job.languages.join(', ')}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="company" className="space-y-8 mt-8">
                {/* Company Info */}
                <Card className="card-premium">
                  <CardHeader>
                    <CardTitle>About {job.company.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-start space-x-4 mb-6">
                      <Avatar className="w-16 h-16">
                        <AvatarImage src={job.company.logo} alt={job.company.name} />
                        <AvatarFallback>{job.company.name.slice(0, 2)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold mb-2">{job.company.name}</h3>
                        <p className="text-muted-foreground mb-3">{job.company.industry}</p>
                        <div className="flex items-center space-x-6 text-sm">
                          <div className="flex items-center space-x-2">
                            <Users className="w-4 h-4" />
                            <span>{job.company.size} employees</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Star className="w-4 h-4 text-yellow-500" />
                            <span>{job.company.rating}/5.0</span>
                          </div>
                          {job.company.verified && (
                            <div className="flex items-center space-x-2">
                              <CheckCircle className="w-4 h-4 text-green-500" />
                              <span>Verified</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <Button variant="outline" className="w-full">
                      <Building className="w-4 h-4 mr-2" />
                      View Company Profile
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="benefits" className="space-y-8 mt-8">
                {/* Benefits */}
                <Card className="card-premium">
                  <CardHeader>
                    <CardTitle>Benefits & Perks</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {job.benefits.map((benefit, index) => (
                        <div key={index} className="flex items-center space-x-3 p-4 bg-muted/50 rounded-lg">
                          <CheckCircle className="w-5 h-5 text-green-500" />
                          <span>{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Visa Sponsorship */}
                {job.visaSponsorship && (
                  <Card className="card-premium border-blue-200 dark:border-blue-800">
                    <CardContent className="p-6">
                      <div className="flex items-center space-x-3 mb-3">
                        <Globe className="w-6 h-6 text-blue-500" />
                        <h4 className="font-semibold text-lg text-blue-700 dark:text-blue-300">Visa Sponsorship Available</h4>
                      </div>
                      <p className="text-blue-600 dark:text-blue-400">
                        This company provides visa sponsorship for qualified candidates.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Apply Card */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle>Ready to Apply?</CardTitle>
                <CardDescription>
                  Join {job.applicants} other candidates who have applied for this position.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button onClick={handleApply} className="w-full button-premium">
                  <Send className="w-4 h-4 mr-2" />
                  Apply Now
                </Button>
                <Button variant="outline" className="w-full">
                  <Download className="w-4 h-4 mr-2" />
                  Save as PDF
                </Button>
                <Button variant="outline" className="w-full">
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Ask a Question
                </Button>
              </CardContent>
            </Card>

            {/* Job Details */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle>Job Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Department</span>
                  <span className="font-medium">{job.department}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Experience Level</span>
                  <span className="font-medium">{job.experience}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Work Model</span>
                  <span className="font-medium">{job.workModel}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Posted</span>
                  <span className="font-medium">{job.posted}</span>
                </div>
                {job.deadline && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Deadline</span>
                    <span className="font-medium">{job.deadline}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      {/* Job Application Modal */}
      <JobApplicationModal
        job={job}
        isOpen={showApplicationModal}
        onClose={() => setShowApplicationModal(false)}
        onSubmit={(applicationData) => {
          console.log('Application submitted:', applicationData)
          // In real app, would submit to API
          alert(`Application submitted successfully for ${applicationData.jobTitle}!`)
          setShowApplicationModal(false)
        }}
      />
    </div>
  )
}
