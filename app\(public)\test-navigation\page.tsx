'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, Navigation, Palette, Layout } from 'lucide-react'

export default function TestNavigationPage() {
  return (
    <div className="pt-16">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-16 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold">Navigation & Theme Test</h1>
              <Badge variant="secondary">Test Page</Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Welcome Section */}
          <div className="text-center space-y-4">
            <h2 className="text-3xl font-bold">
              Navigation & Theme Consistency Test 🎨
            </h2>
            <p className="text-muted-foreground text-lg">
              This page tests that navigation and theming work correctly across all routes
            </p>
          </div>

          {/* Test Results */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="card-premium">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Navigation className="w-5 h-5" />
                  <span>Navigation Test</span>
                </CardTitle>
                <CardDescription>
                  Check if navigation bar appears and functions correctly
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Navigation bar visible</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Logo and brand name present</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Navigation links functional</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Theme toggle available</span>
                </div>
              </CardContent>
            </Card>

            <Card className="card-professional">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Palette className="w-5 h-5" />
                  <span>Theme Test</span>
                </CardTitle>
                <CardDescription>
                  Verify theme consistency and switching functionality
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Theme provider active</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">CSS variables working</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Background pattern visible</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Component styling consistent</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Layout className="w-5 h-5" />
                  <span>Layout Test</span>
                </CardTitle>
                <CardDescription>
                  Check layout consistency and responsive design
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Shared layout applied</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Footer present</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Floating action button visible</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Responsive design working</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Test navigation to different sections
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button asChild className="button-premium w-full">
                  <a href="/jobs">Test Jobs Page</a>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <a href="/login">Test Auth Page</a>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <a href="/">Back to Home</a>
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Premium Theme Showcase */}
          <Card className="card-premium">
            <CardHeader>
              <CardTitle>🌟 Premium Theme System</CardTitle>
              <CardDescription>
                Immersive, top-dollar professional themes with rich color environments
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="p-6 rounded-xl bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/30 theme-glow">
                  <h4 className="font-bold text-primary mb-2">Deep Ocean</h4>
                  <p className="text-sm text-muted-foreground">Immersive dark blue environment with rich oceanic depths and premium corporate aesthetics</p>
                </div>
                <div className="p-6 rounded-xl bg-gradient-to-br from-green-500/20 to-green-600/10 border border-green-500/30 shadow-xl shadow-green-500/10">
                  <h4 className="font-bold text-green-400 mb-2">Dark Forest</h4>
                  <p className="text-sm text-muted-foreground">Rich dark green atmosphere with forest depths and sophisticated natural tones</p>
                </div>
                <div className="p-6 rounded-xl bg-gradient-to-br from-gray-500/20 to-gray-600/10 border border-gray-500/30 shadow-xl shadow-gray-500/10">
                  <h4 className="font-bold text-gray-300 mb-2">Midnight</h4>
                  <p className="text-sm text-muted-foreground">Premium dark theme with sophisticated tech aesthetics and elegant contrasts</p>
                </div>
              </div>

              <div className="p-6 rounded-xl bg-gradient-to-r from-accent/60 to-accent/40 border border-primary/20 theme-glow">
                <h4 className="font-bold mb-3 text-primary">🚀 Premium Features:</h4>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li>• <strong>Immersive Color Environments:</strong> Each theme creates a unified, atmospheric experience</li>
                  <li>• <strong>Dark Theme Mastery:</strong> Rich dark green and deep ocean blue themes</li>
                  <li>• <strong>Premium Visual Effects:</strong> Advanced shadows, glows, and backdrop blur</li>
                  <li>• <strong>Professional Grade Styling:</strong> Top-dollar appearance suitable for enterprise</li>
                  <li>• <strong>Enhanced Component Integration:</strong> All elements harmonize with theme colors</li>
                  <li>• <strong>Sophisticated Animations:</strong> Smooth transitions and micro-interactions</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Instructions */}
          <Card className="card-professional">
            <CardHeader>
              <CardTitle>Test Instructions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">1. Navigation Test:</h4>
                <p className="text-sm text-muted-foreground">
                  • Check that the navigation bar is visible at the top
                  • Verify all navigation links work correctly
                  • Test the theme toggle functionality
                  • Ensure mobile menu works on smaller screens
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">2. Theme Test:</h4>
                <p className="text-sm text-muted-foreground">
                  • Toggle between light and dark themes
                  • Verify colors change consistently across all elements
                  • Check that the background pattern adapts to theme
                  • Ensure text remains readable in both themes
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">3. Layout Test:</h4>
                <p className="text-sm text-muted-foreground">
                  • Navigate to different pages (Jobs, Auth, Dashboard)
                  • Verify navigation persists across all pages
                  • Check that layout is consistent
                  • Test responsive behavior on different screen sizes
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
