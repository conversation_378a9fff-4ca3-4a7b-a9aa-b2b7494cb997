import { NextRequest, NextResponse } from 'next/server'
import { adminService } from '@/lib/services/admin.service'
import { withAdminAuth } from '@/lib/middleware/auth.middleware'
import { createSuccessResponse } from '@/lib/api/route-handler'
import { connectDB } from '@/lib/database/connection'

interface RouteParams {
  params: {
    id: string
  }
}

// PUT /api/admin/companies/[id]/verify - Verify or reject company
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB()
    
    const companyId = params.id
    const body = await request.json()
    const { verified, notes } = body

    if (typeof verified !== 'boolean') {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Verified status must be a boolean'
          },
          meta: {
            timestamp: new Date().toISOString(),
            requestId: crypto.randomUUID()
          }
        },
        { status: 400 }
      )
    }

    await adminService.verifyCompany(companyId, verified, notes)
    
    return createSuccessResponse({ 
      message: `Company ${verified ? 'verified' : 'rejected'} successfully` 
    })
  } catch (error: any) {
    console.error('Admin company verification error:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'COMPANY_VERIFICATION_ERROR',
          message: error.message || 'Failed to verify company'
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID()
        }
      },
      { status: error.statusCode || 500 }
    )
  }
}

export const dynamic = 'force-dynamic'
