import { NextRequest, NextResponse } from 'next/server'
import { adminService } from '@/lib/services/admin.service'
import { withAdminAuth } from '@/lib/middleware/auth.middleware'
import { createSuccessResponse } from '@/lib/api/route-handler'
import { connectDB } from '@/lib/database/connection'

// GET /api/admin/dashboard - Get admin dashboard data
export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const dashboardData = await adminService.getDashboardData()
    
    return createSuccessResponse(dashboardData)
  } catch (error: any) {
    console.error('Admin dashboard error:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'DASHBOARD_ERROR',
          message: error.message || 'Failed to fetch dashboard data'
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID()
        }
      },
      { status: error.statusCode || 500 }
    )
  }
}

// Apply admin authentication middleware
export const dynamic = 'force-dynamic'
