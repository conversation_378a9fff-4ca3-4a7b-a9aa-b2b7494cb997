import { NextRequest, NextResponse } from 'next/server'
import { adminService } from '@/lib/services/admin.service'
import { withAdminAuth } from '@/lib/middleware/auth.middleware'
import { createSuccessResponse } from '@/lib/api/route-handler'
import { connectDB } from '@/lib/database/connection'

// GET /api/admin/users - Get users with pagination and filters
export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const role = searchParams.get('role') || undefined
    const isActive = searchParams.get('isActive') ? searchParams.get('isActive') === 'true' : undefined
    const isEmailVerified = searchParams.get('isEmailVerified') ? searchParams.get('isEmailVerified') === 'true' : undefined
    const search = searchParams.get('search') || undefined
    const dateFrom = searchParams.get('dateFrom') || undefined
    const dateTo = searchParams.get('dateTo') || undefined

    const filters = {
      role,
      isActive,
      isEmailVerified,
      search,
      dateFrom,
      dateTo
    }

    const result = await adminService.getUsers(page, limit, filters)
    
    return createSuccessResponse(result)
  } catch (error: any) {
    console.error('Admin users fetch error:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'USERS_FETCH_ERROR',
          message: error.message || 'Failed to fetch users'
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID()
        }
      },
      { status: error.statusCode || 500 }
    )
  }
}

export const dynamic = 'force-dynamic'
