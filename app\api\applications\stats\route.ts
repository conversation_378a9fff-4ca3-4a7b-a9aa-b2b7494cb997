import { NextRequest, NextResponse } from 'next/server'
import { Application } from '@/lib/models/application.model'
import { withAuth } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/db'

// GET /api/applications/stats - Get application statistics
async function getApplicationStatsHandler(
  request: NextRequest,
  { user }: { user: any }
) {
  try {
    await connectDB()

    // Get all user applications
    const applications = await Application.find({ userId: user._id })

    const total = applications.length

    if (total === 0) {
      return NextResponse.json({
        total: 0,
        submitted: 0,
        underReview: 0,
        interviewed: 0,
        offers: 0,
        rejected: 0,
        responseRate: 0,
        averageResponseTime: 0
      })
    }

    // Count by status
    const statusCounts = applications.reduce((acc, app) => {
      acc[app.status] = (acc[app.status] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const submitted = statusCounts.submitted || 0
    const underReview = statusCounts.under_review || 0
    const interviewed = (statusCounts.interview_scheduled || 0) + (statusCounts.interviewed || 0)
    const offers = statusCounts.offer_extended || 0
    const rejected = statusCounts.rejected || 0

    // Calculate response rate (applications that received any response)
    const responded = total - submitted
    const responseRate = total > 0 ? Math.round((responded / total) * 100) : 0

    // Calculate average response time
    let totalResponseTime = 0
    let responseCount = 0

    applications.forEach(app => {
      if (app.timeline && app.timeline.length > 1) {
        // Find first non-submitted timeline event
        const firstResponse = app.timeline.find(event => event.type !== 'submitted')
        if (firstResponse) {
          const submittedTime = app.submittedAt.getTime()
          const responseTime = firstResponse.createdAt.getTime()
          const timeDiff = responseTime - submittedTime
          
          if (timeDiff > 0) {
            totalResponseTime += timeDiff
            responseCount++
          }
        }
      }
    })

    const averageResponseTime = responseCount > 0 
      ? Math.round(totalResponseTime / responseCount / (1000 * 60 * 60)) // Convert to hours
      : 0

    return NextResponse.json({
      total,
      submitted,
      underReview,
      interviewed,
      offers,
      rejected,
      responseRate,
      averageResponseTime
    })

  } catch (error) {
    console.error('Get application stats error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export const GET = withAuth(getApplicationStatsHandler, { 
  requiredRoles: ['job_seeker'] 
})
