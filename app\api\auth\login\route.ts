import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/services/auth.service'
import { withValidation, schemas, rateLimiters } from '@/lib/middleware/validation.middleware'
import { connectDB } from '@/lib/db'

async function loginHandler(request: NextRequest, data: any) {
  try {
    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown'
    const rateLimit = rateLimiters.auth(clientIP)
    
    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          error: 'Too many login attempts. Please try again later.',
          retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
        },
        { status: 429 }
      )
    }

    // Connect to database
    await connectDB()

    const { email, password, rememberMe } = data

    // Use auth service for login
    const result = await authService.login({ email, password, rememberMe })

    return NextResponse.json({
      message: 'Login successful',
      token: result.tokens.accessToken,
      refreshToken: result.tokens.refreshToken,
      user: result.user,
      company: result.company,
      client: result.client,
      emailVerificationRequired: result.emailVerificationRequired
    })

  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export const POST = withValidation(schemas.login, loginHandler)
