import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/services/auth.service'
import { withValidation, schemas, rateLimiters } from '@/lib/middleware/validation.middleware'
import { connectDB } from '@/lib/db'

async function registerHandler(request: NextRequest, data: any) {
  try {
    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown'
    const rateLimit = rateLimiters.auth(clientIP)
    
    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          error: 'Too many registration attempts. Please try again later.',
          retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
        },
        { status: 429 }
      )
    }

    // Connect to database
    await connectDB()

    const { email, password, firstName, lastName, role, phone, location, company } = data

    // Prepare registration data for auth service
    const registerData = {
      email,
      password,
      firstName,
      lastName,
      role,
      phone,
      location,
      company
    }

    // Use auth service for registration
    const result = await authService.register(registerData)

    return NextResponse.json({
      message: 'Registration successful',
      token: result.tokens.accessToken,
      refreshToken: result.tokens.refreshToken,
      user: result.user,
      company: result.company,
      client: result.client,
      emailVerificationRequired: result.emailVerificationRequired
    }, { status: 201 })

  } catch (error) {
    console.error('Registration error:', error)
    
    // Handle duplicate key error
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export const POST = withValidation(schemas.register, registerHandler)
