import { NextRequest, NextResponse } from 'next/server'
import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'
import { withAuth } from '@/lib/middleware/auth.middleware'
import { validateObjectId } from '@/lib/middleware/validation.middleware'
import { connectDB } from '@/lib/db'

// POST /api/companies/[id]/follow - Follow company
async function followCompanyHandler(
  request: NextRequest,
  { params, user }: { params: { id: string }; user: any }
) {
  try {
    await connectDB()

    const { id } = params

    // Validate ObjectId
    if (!validateObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid company ID' },
        { status: 400 }
      )
    }

    // Find company
    const company = await Company.findById(id)
    if (!company || !company.isActive) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Check if user is already following
    const userDoc = await User.findById(user._id)
    if (!userDoc) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    const followedCompanies = userDoc.followedCompanies || []
    if (followedCompanies.includes(id)) {
      return NextResponse.json(
        { error: 'Already following this company' },
        { status: 409 }
      )
    }

    // Add company to user's followed list
    await User.findByIdAndUpdate(user._id, {
      $addToSet: { followedCompanies: id }
    })

    // Increment company followers count
    await Company.findByIdAndUpdate(id, {
      $inc: { followersCount: 1 }
    })

    return NextResponse.json({
      message: 'Successfully followed company',
      following: true
    })

  } catch (error) {
    console.error('Follow company error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/companies/[id]/follow - Unfollow company
async function unfollowCompanyHandler(
  request: NextRequest,
  { params, user }: { params: { id: string }; user: any }
) {
  try {
    await connectDB()

    const { id } = params

    // Validate ObjectId
    if (!validateObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid company ID' },
        { status: 400 }
      )
    }

    // Find company
    const company = await Company.findById(id)
    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Check if user is following
    const userDoc = await User.findById(user._id)
    if (!userDoc) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    const followedCompanies = userDoc.followedCompanies || []
    if (!followedCompanies.includes(id)) {
      return NextResponse.json(
        { error: 'Not following this company' },
        { status: 409 }
      )
    }

    // Remove company from user's followed list
    await User.findByIdAndUpdate(user._id, {
      $pull: { followedCompanies: id }
    })

    // Decrement company followers count
    await Company.findByIdAndUpdate(id, {
      $inc: { followersCount: -1 }
    })

    return NextResponse.json({
      message: 'Successfully unfollowed company',
      following: false
    })

  } catch (error) {
    console.error('Unfollow company error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export const POST = withAuth(followCompanyHandler, { 
  requiredRoles: ['job_seeker'] 
})

export const DELETE = withAuth(unfollowCompanyHandler, { 
  requiredRoles: ['job_seeker'] 
})
