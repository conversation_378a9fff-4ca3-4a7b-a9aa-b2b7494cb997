import { NextRequest, NextResponse } from 'next/server'
import { Company } from '@/lib/models/company.model'
import { Job } from '@/lib/models/job.model'
import { withAuth } from '@/lib/middleware/auth.middleware'
import { validateObjectId } from '@/lib/middleware/validation.middleware'
import { connectDB } from '@/lib/db'

// GET /api/companies/[id] - Get company by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const { id } = params

    // Validate ObjectId
    if (!validateObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid company ID' },
        { status: 400 }
      )
    }

    // Find company
    const company = await Company.findById(id)
      .populate('createdBy', 'profile.firstName profile.lastName')

    if (!company || !company.isActive) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Get company jobs count
    const jobsCount = await Job.countDocuments({ 
      company: id, 
      isActive: true 
    })

    // Update jobs count if different
    if (company.jobsCount !== jobsCount) {
      await Company.findByIdAndUpdate(id, { jobsCount })
      company.jobsCount = jobsCount
    }

    return NextResponse.json({ company })

  } catch (error) {
    console.error('Get company error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/companies/[id] - Update company (company admin only)
async function updateCompanyHandler(
  request: NextRequest,
  { params, user }: { params: { id: string }; user: any }
) {
  try {
    await connectDB()

    const { id } = params

    // Validate ObjectId
    if (!validateObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid company ID' },
        { status: 400 }
      )
    }

    // Find company
    const company = await Company.findById(id)
    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Check permissions
    const canEdit = user.role === 'admin' || 
                   (user.companyId && company._id.toString() === user.companyId.toString())

    if (!canEdit) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Get update data
    const updateData = await request.json()
    
    // Update company
    const updatedCompany = await Company.findByIdAndUpdate(
      id,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    )

    return NextResponse.json({
      message: 'Company updated successfully',
      company: updatedCompany
    })

  } catch (error) {
    console.error('Update company error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/companies/[id] - Delete company (admin only)
async function deleteCompanyHandler(
  request: NextRequest,
  { params, user }: { params: { id: string }; user: any }
) {
  try {
    await connectDB()

    const { id } = params

    // Validate ObjectId
    if (!validateObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid company ID' },
        { status: 400 }
      )
    }

    // Find company
    const company = await Company.findById(id)
    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Only admin can delete companies
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Soft delete (mark as inactive)
    await Company.findByIdAndUpdate(id, { 
      isActive: false,
      deletedAt: new Date(),
      deletedBy: user._id
    })

    // Also deactivate all company jobs
    await Job.updateMany(
      { company: id },
      { 
        isActive: false,
        deletedAt: new Date(),
        deletedBy: user._id
      }
    )

    return NextResponse.json({
      message: 'Company deleted successfully'
    })

  } catch (error) {
    console.error('Delete company error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export const PUT = withAuth(updateCompanyHandler, { 
  requiredRoles: ['company_admin', 'admin'] 
})

export const DELETE = withAuth(deleteCompanyHandler, { 
  requiredRoles: ['admin'] 
})
