import { NextRequest, NextResponse } from 'next/server'
import { Company } from '@/lib/models/company.model'
import { withAuth } from '@/lib/middleware/auth.middleware'
import { withValidation, schemas, rateLimiters } from '@/lib/middleware/validation.middleware'
import { connectDB } from '@/lib/db'

// GET /api/companies - Search companies
async function getCompaniesHandler(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown'
    const rateLimit = rateLimiters.search(clientIP)
    
    if (!rateLimit.allowed) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      )
    }

    await connectDB()

    const url = new URL(request.url)
    const q = url.searchParams.get('q') || ''
    const industry = url.searchParams.get('industry') || ''
    const location = url.searchParams.get('location') || ''
    const size = url.searchParams.get('size') || ''
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '20')

    // Build search query
    const searchQuery: any = { isActive: true }

    // Text search
    if (q) {
      searchQuery.$or = [
        { name: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } },
        { industry: { $regex: q, $options: 'i' } },
        { specialties: { $in: [new RegExp(q, 'i')] } }
      ]
    }

    // Industry filter
    if (industry) {
      searchQuery.industry = industry
    }

    // Location filter
    if (location) {
      searchQuery.$or = [
        { 'location.city': { $regex: location, $options: 'i' } },
        { 'location.state': { $regex: location, $options: 'i' } }
      ]
    }

    // Size filter
    if (size) {
      searchQuery.size = size
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Execute search
    const [companies, total] = await Promise.all([
      Company.find(searchQuery)
        .select('name description industry location size logo followersCount jobsCount')
        .sort({ followersCount: -1, createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Company.countDocuments(searchQuery)
    ])

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      companies,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    })

  } catch (error) {
    console.error('Companies search error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/companies - Create company (company admin only)
async function createCompanyHandler(
  request: NextRequest,
  data: any,
  { user }: { user: any }
) {
  try {
    await connectDB()

    // Verify user has permission to create companies
    if (!['company_admin', 'admin'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Check if user already has a company
    if (user.companyId && user.role !== 'admin') {
      return NextResponse.json(
        { error: 'User already has a company' },
        { status: 400 }
      )
    }

    // Create company
    const company = new Company({
      ...data,
      createdBy: user._id,
      isActive: true,
      followersCount: 0,
      jobsCount: 0
    })

    await company.save()

    // Update user's companyId if they don't have one
    if (!user.companyId) {
      await User.findByIdAndUpdate(user._id, { companyId: company._id })
    }

    return NextResponse.json({
      message: 'Company created successfully',
      company
    }, { status: 201 })

  } catch (error) {
    console.error('Create company error:', error)
    
    // Handle duplicate company name
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'Company name already exists' },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export const GET = getCompaniesHandler
export const POST = withAuth(
  withValidation(schemas.createCompany, createCompanyHandler),
  { requiredRoles: ['company_admin', 'admin'] }
)
