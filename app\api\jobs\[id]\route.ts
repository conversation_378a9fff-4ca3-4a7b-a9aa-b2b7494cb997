import { NextRequest, NextResponse } from 'next/server'
import { Job } from '@/lib/models/job.model'
import { withAuth } from '@/lib/middleware/auth.middleware'
import { validateObjectId } from '@/lib/middleware/validation.middleware'
import { connectDB } from '@/lib/db'

// GET /api/jobs/[id] - Get job by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const { id } = params

    // Validate ObjectId
    if (!validateObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid job ID' },
        { status: 400 }
      )
    }

    // Find job
    const job = await Job.findById(id)
      .populate('company', 'name logo location industry website description')
      .populate('postedBy', 'profile.firstName profile.lastName')

    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    // Increment view count
    await Job.findByIdAndUpdate(id, { $inc: { viewsCount: 1 } })

    return NextResponse.json({ job })

  } catch (error) {
    console.error('Get job error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/jobs/[id] - Update job (company admin/recruiter only)
async function updateJobHandler(
  request: NextRequest,
  { params, user }: { params: { id: string }; user: any }
) {
  try {
    await connectDB()

    const { id } = params

    // Validate ObjectId
    if (!validateObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid job ID' },
        { status: 400 }
      )
    }

    // Find job
    const job = await Job.findById(id)
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    // Check permissions
    const canEdit = user.role === 'admin' || 
                   (user.companyId && job.company.toString() === user.companyId.toString())

    if (!canEdit) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Get update data
    const updateData = await request.json()
    
    // Update job
    const updatedJob = await Job.findByIdAndUpdate(
      id,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).populate('company', 'name logo location industry')

    return NextResponse.json({
      message: 'Job updated successfully',
      job: updatedJob
    })

  } catch (error) {
    console.error('Update job error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/jobs/[id] - Delete job (company admin/recruiter only)
async function deleteJobHandler(
  request: NextRequest,
  { params, user }: { params: { id: string }; user: any }
) {
  try {
    await connectDB()

    const { id } = params

    // Validate ObjectId
    if (!validateObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid job ID' },
        { status: 400 }
      )
    }

    // Find job
    const job = await Job.findById(id)
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    // Check permissions
    const canDelete = user.role === 'admin' || 
                     (user.companyId && job.company.toString() === user.companyId.toString())

    if (!canDelete) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Soft delete (mark as inactive)
    await Job.findByIdAndUpdate(id, { 
      isActive: false,
      deletedAt: new Date(),
      deletedBy: user._id
    })

    return NextResponse.json({
      message: 'Job deleted successfully'
    })

  } catch (error) {
    console.error('Delete job error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export const PUT = withAuth(updateJobHandler, { 
  requiredRoles: ['company_admin', 'recruiter', 'admin'] 
})

export const DELETE = withAuth(deleteJobHandler, { 
  requiredRoles: ['company_admin', 'recruiter', 'admin'] 
})
