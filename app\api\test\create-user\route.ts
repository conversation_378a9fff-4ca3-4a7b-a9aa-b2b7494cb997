import { NextRequest, NextResponse } from 'next/server'
import { User } from '@/lib/models/user.model'
import { connectDB } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    const { email, password, firstName, lastName, role } = await request.json()
    
    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() })
    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists' },
        { status: 400 }
      )
    }
    
    // Create new user
    const user = new User({
      email: email.toLowerCase(),
      password,
      role: role || 'job_seeker',
      profile: {
        firstName,
        lastName,
        location: {}
      },
      preferences: {
        emailNotifications: true,
        jobAlerts: true,
        marketingEmails: false,
        theme: 'system'
      },
      isActive: true,
      isEmailVerified: true // For testing purposes
    })
    
    await user.save()
    
    return NextResponse.json({
      message: 'User created successfully',
      user: {
        id: user._id,
        email: user.email,
        role: user.role,
        profile: user.profile
      }
    })
    
  } catch (error) {
    console.error('Create user error:', error)
    return NextResponse.json(
      { error: 'Failed to create user', details: error.message },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    await connectDB()
    
    const users = await User.find({}).select('-password').limit(10)
    
    return NextResponse.json({
      message: 'Users retrieved successfully',
      count: users.length,
      users
    })
    
  } catch (error) {
    console.error('Get users error:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve users' },
      { status: 500 }
    )
  }
}
