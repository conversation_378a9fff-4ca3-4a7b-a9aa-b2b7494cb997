import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { Client } from '@/lib/models/client.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')
    
    if (!email) {
      return NextResponse.json({ error: 'Email parameter required' }, { status: 400 })
    }
    
    // Find user
    const user = await User.findOne({ email: email.toLowerCase() })
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }
    
    let company = null
    let client = null
    
    // Get company data if user is company admin
    if (user.role === 'company_admin' && user.companyId) {
      company = await Company.findById(user.companyId)
    }
    
    // Get client data if user is job seeker
    if (user.role === 'job_seeker') {
      client = await Client.findOne({ user: user._id })
    }
    
    return NextResponse.json({
      user: {
        _id: user._id,
        email: user.email,
        role: user.role,
        profile: user.profile,
        companyId: user.companyId,
        isEmailVerified: user.isEmailVerified,
        createdAt: user.createdAt
      },
      company: company ? {
        _id: company._id,
        name: company.name,
        slug: company.slug,
        description: company.description,
        website: company.website,
        industry: company.industry,
        size: company.size,
        contact: company.contact,
        locations: company.locations,
        admins: company.admins,
        teamMembers: company.teamMembers,
        createdBy: company.createdBy,
        createdAt: company.createdAt
      } : null,
      client: client ? {
        _id: client._id,
        headline: client.headline,
        summary: client.summary,
        experience: client.experience,
        jobPreferences: client.jobPreferences,
        privacy: client.privacy,
        activity: client.activity,
        profileCompleteness: client.activity?.profileCompleteness,
        createdAt: client.createdAt
      } : null
    })
    
  } catch (error) {
    console.error('Test API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
