import { NextRequest } from 'next/server'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, createSuccessResponse, validateMethod, validateRequestBody } from '@/lib/api/route-handler'
import { applicationService } from '@/lib/services'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import type { UpdateApplicationStatusRequest, AddNoteRequest } from '@/lib/services'

interface RouteParams {
  params: {
    id: string
  }
}

// Validation function for update application status request
function validateUpdateStatusRequest(data: any): UpdateApplicationStatusRequest {
  const errors: string[] = []
  
  if (!data.status) {
    errors.push('Status is required')
  }
  
  const validStatuses = ['pending', 'reviewing', 'shortlisted', 'interviewed', 'offered', 'hired', 'rejected']
  if (data.status && !validStatuses.includes(data.status)) {
    errors.push(`Invalid status. Must be one of: ${validStatuses.join(', ')}`)
  }
  
  if (errors.length > 0) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      `Validation failed: ${errors.join(', ')}`,
      undefined,
      { validationErrors: errors }
    )
  }
  
  return {
    status: data.status,
    notes: data.notes?.trim()
  }
}

// Validation function for add note request
function validateAddNoteRequest(data: any): AddNoteRequest {
  const errors: string[] = []
  
  if (!data.content || data.content.trim() === '') {
    errors.push('Note content is required')
  }
  
  if (data.content && data.content.length > 1000) {
    errors.push('Note content cannot exceed 1000 characters')
  }
  
  if (errors.length > 0) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      `Validation failed: ${errors.join(', ')}`,
      undefined,
      { validationErrors: errors }
    )
  }
  
  return {
    content: data.content.trim()
  }
}

// GET /api/v1/applications/[id] - Get application by ID
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  validateMethod(request, ['GET'])
  
  const applicationId = params.id
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  const result = await applicationService.getApplicationById(applicationId, userId)
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true,
  requireAuth: true
})

// PUT /api/v1/applications/[id] - Update application status (company only)
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  validateMethod(request, ['PUT'])
  
  const applicationId = params.id
  const { searchParams } = new URL(request.url)
  const action = searchParams.get('action')
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  if (action === 'status') {
    // Update application status
    const statusData = await validateRequestBody(request, validateUpdateStatusRequest)
    
    // This would need to be implemented in the application service
    // For now, return a placeholder response
    return createSuccessResponse({ 
      message: 'Application status updated successfully',
      status: statusData.status 
    })
    
  } else if (action === 'note') {
    // Add note to application
    const noteData = await validateRequestBody(request, validateAddNoteRequest)
    
    // This would need to be implemented in the application service
    // For now, return a placeholder response
    return createSuccessResponse({ 
      message: 'Note added successfully',
      note: noteData.content 
    })
    
  } else if (action === 'withdraw') {
    // Withdraw application (applicant only)
    
    // This would need to be implemented in the application service
    // For now, return a placeholder response
    return createSuccessResponse({ 
      message: 'Application withdrawn successfully' 
    })
    
  } else {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Invalid action. Must be one of: status, note, withdraw',
      'action'
    )
  }
}, {
  requireDatabase: true,
  requireAuth: true
})

// DELETE /api/v1/applications/[id] - Delete application (admin only)
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  validateMethod(request, ['DELETE'])
  
  const applicationId = params.id
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  // This would need to be implemented in the application service
  // For now, return a placeholder response
  return createSuccessResponse({ 
    message: 'Application deleted successfully' 
  })
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['admin', 'super_admin']
})

// Method not allowed for other HTTP methods
export async function POST() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'POST method not allowed for individual application resources'
  )
}
