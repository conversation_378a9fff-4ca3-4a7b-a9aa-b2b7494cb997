import { NextRequest } from 'next/server'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, createSuccessResponse, validateMethod, validateRequestBody } from '@/lib/api/route-handler'
import { applicationService, validationService } from '@/lib/services'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import type { CreateApplicationRequest } from '@/lib/services'

// Validation function for create application request
function validateCreateApplicationRequest(data: any): CreateApplicationRequest {
  const errors: string[] = []
  
  // Required fields
  if (!data.jobId) errors.push('Job ID is required')
  
  // Validate URLs if provided
  if (data.resumeUrl && !/^https?:\/\/.+/.test(data.resumeUrl)) {
    errors.push('Resume URL must be a valid URL')
  }
  
  if (data.portfolioUrl && !/^https?:\/\/.+/.test(data.portfolioUrl)) {
    errors.push('Portfolio URL must be a valid URL')
  }
  
  // Validate expected salary if provided
  if (data.expectedSalary) {
    if (data.expectedSalary.amount && data.expectedSalary.amount < 0) {
      errors.push('Expected salary amount cannot be negative')
    }
    
    if (data.expectedSalary.currency && !['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'INR'].includes(data.expectedSalary.currency)) {
      errors.push('Invalid currency')
    }
    
    if (data.expectedSalary.period && !['hourly', 'monthly', 'yearly'].includes(data.expectedSalary.period)) {
      errors.push('Invalid salary period')
    }
  }
  
  // Validate available start date if provided
  if (data.availableStartDate) {
    const startDate = new Date(data.availableStartDate)
    if (startDate < new Date()) {
      errors.push('Available start date must be in the future')
    }
  }
  
  // Validate custom answers if provided
  if (data.customAnswers && Array.isArray(data.customAnswers)) {
    data.customAnswers.forEach((answer: any, index: number) => {
      if (!answer.question || !answer.answer) {
        errors.push(`Custom answer ${index + 1} must have both question and answer`)
      }
    })
  }
  
  if (errors.length > 0) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      `Validation failed: ${errors.join(', ')}`,
      undefined,
      { validationErrors: errors }
    )
  }
  
  return {
    jobId: data.jobId,
    coverLetter: data.coverLetter?.trim(),
    resumeUrl: data.resumeUrl?.trim(),
    portfolioUrl: data.portfolioUrl?.trim(),
    expectedSalary: data.expectedSalary,
    availableStartDate: data.availableStartDate ? new Date(data.availableStartDate) : undefined,
    customAnswers: data.customAnswers?.map((answer: any) => ({
      question: answer.question.trim(),
      answer: answer.answer.trim()
    }))
  }
}

// GET /api/v1/applications - Get applications (user's own applications or job applications for company)
export const GET = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['GET'])
  
  const { searchParams } = new URL(request.url)
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  // Pagination
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '10')
  const status = searchParams.get('status') || undefined
  
  // Check if this is for a specific job (company view)
  const jobId = searchParams.get('jobId')
  
  let result
  
  if (jobId) {
    // Company viewing applications for their job
    result = await applicationService.getJobApplications(jobId, userId, page, limit, status)
  } else {
    // User viewing their own applications
    result = await applicationService.getUserApplications(userId, page, limit, status)
  }
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true,
  requireAuth: true
})

// POST /api/v1/applications - Create a new application
export const POST = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['POST'])
  
  const applicationData = await validateRequestBody(request, validateCreateApplicationRequest)
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  const result = await applicationService.createApplication(applicationData, userId)
  
  return createSuccessResponse(result, 201)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['job_seeker']
})

// Method not allowed for other HTTP methods
export async function PUT() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PUT method not allowed for applications collection'
  )
}

export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed for applications collection'
  )
}
