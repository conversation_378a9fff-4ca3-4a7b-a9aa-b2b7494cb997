import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Application } from '@/lib/models/application.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '5')

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Get recent applications
    const applications = await Application.find({ client: client._id })
      .populate('job', 'title company location salary')
      .sort({ createdAt: -1 })
      .limit(limit)
      .lean()

    // Format applications
    const formattedApplications = applications.map(app => ({
      id: app._id.toString(),
      company: app.job?.company || 'Unknown Company',
      position: app.job?.title || 'Unknown Position',
      status: app.status,
      statusText: getStatusText(app.status),
      statusColor: getStatusColor(app.status),
      appliedDate: app.createdAt.toISOString(),
      interviewDate: app.interviewDate?.toISOString(),
      notes: app.notes
    }))

    return NextResponse.json({
      success: true,
      data: formattedApplications
    })

  } catch (error) {
    console.error('Get recent applications error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch recent applications' },
      { status: 500 }
    )
  }
}

function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'applied': 'Application Sent',
    'under_review': 'Under Review',
    'interview_scheduled': 'Interview Scheduled',
    'offer_received': 'Offer Received',
    'rejected': 'Not Selected',
    'withdrawn': 'Withdrawn'
  }
  return statusMap[status] || status
}

function getStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    'applied': 'bg-blue-500',
    'under_review': 'bg-yellow-500',
    'interview_scheduled': 'bg-purple-500',
    'offer_received': 'bg-green-500',
    'rejected': 'bg-red-500',
    'withdrawn': 'bg-gray-500'
  }
  return colorMap[status] || 'bg-gray-500'
}
