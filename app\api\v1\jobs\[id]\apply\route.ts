import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Job } from '@/lib/models/job.model'
import { Application } from '@/lib/models/application.model'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const jobId = params.id
    const applicationData = await request.json()

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Verify job exists and is active
    const job = await Job.findById(jobId)
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    if (job.status !== 'active') {
      return NextResponse.json(
        { error: 'This job is no longer accepting applications' },
        { status: 400 }
      )
    }

    // Check if already applied
    const existingApplication = await Application.findOne({
      client: client._id,
      job: jobId
    })

    if (existingApplication) {
      return NextResponse.json(
        { error: 'You have already applied to this job' },
        { status: 400 }
      )
    }

    // Create application
    const application = new Application({
      client: client._id,
      job: jobId,
      status: 'applied',
      coverLetter: applicationData.coverLetter,
      resumeId: applicationData.resumeId,
      additionalDocuments: applicationData.additionalDocuments || [],
      customAnswers: applicationData.customAnswers || {},
      source: applicationData.source || 'direct',
      timeline: [{
        status: 'applied',
        date: new Date(),
        notes: 'Application submitted successfully'
      }]
    })

    await application.save()

    // Update client application stats
    await Client.findByIdAndUpdate(client._id, {
      $inc: { 
        'applicationStats.totalApplications': 1,
        'applicationStats.pendingApplications': 1
      }
    })

    // Update job application count
    await Job.findByIdAndUpdate(jobId, {
      $inc: { 'stats.totalApplications': 1 }
    })

    return NextResponse.json({
      success: true,
      message: 'Application submitted successfully',
      data: {
        applicationId: application._id,
        status: application.status,
        appliedDate: application.createdAt
      }
    })

  } catch (error) {
    console.error('Job application error:', error)
    return NextResponse.json(
      { error: 'Failed to submit application' },
      { status: 500 }
    )
  }
}
