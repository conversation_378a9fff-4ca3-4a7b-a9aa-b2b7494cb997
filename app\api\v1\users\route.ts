import { NextRequest } from 'next/server'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, createSuccessResponse, validate<PERSON>ethod } from '@/lib/api/route-handler'
import { userService } from '@/lib/services'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'

// GET /api/v1/users - Get users with pagination and filters
export const GET = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['GET'])
  
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '10')
  const role = searchParams.get('role') || undefined
  const isActive = searchParams.get('isActive') ? searchParams.get('isActive') === 'true' : undefined
  
  const result = await userService.getUsers(page, limit, { role, isActive })
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['admin', 'super_admin']
})

// Method not allowed for other HTTP methods
export async function POST() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'POST method not allowed. Use /api/v1/auth/register for user registration'
  )
}

export async function PUT() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PUT method not allowed for users collection'
  )
}

export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed for users collection'
  )
}
