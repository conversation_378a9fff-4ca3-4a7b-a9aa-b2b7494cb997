'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  Users,
  Building,
  Briefcase,
  FileText,
  DollarSign,
  Activity,
  Calendar,
  Download,
  BarChart3
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface AdminAnalyticsProps {
  className?: string
}

export function AdminAnalytics({ className }: AdminAnalyticsProps) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('overview')
  const [timeRange, setTimeRange] = useState('30d')

  // Mock analytics data - in real implementation, this would come from API
  const mockMetrics = {
    users: {
      total: 15420,
      growth: 12.5,
      active: 8930,
      new: 245
    },
    companies: {
      total: 1250,
      growth: 8.3,
      verified: 980,
      new: 18
    },
    jobs: {
      total: 3420,
      growth: 15.2,
      active: 2890,
      new: 67
    },
    applications: {
      total: 28450,
      growth: 22.1,
      successful: 1240,
      new: 156
    },
    revenue: {
      total: 125000,
      growth: 18.7,
      recurring: 98000,
      new: 4500
    }
  }

  const MetricCard = ({ 
    title, 
    value, 
    growth, 
    icon: Icon, 
    color,
    subtitle 
  }: {
    title: string
    value: number | string
    growth?: number
    icon: any
    color: string
    subtitle?: string
  }) => (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
            {subtitle && (
              <p className="text-xs text-muted-foreground">{subtitle}</p>
            )}
            {growth !== undefined && (
              <div className="flex items-center mt-1">
                {growth > 0 ? (
                  <TrendingUp className="w-3 h-3 text-green-600 mr-1" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-600 mr-1" />
                )}
                <span className={`text-xs ${growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(growth)}% vs last period
                </span>
              </div>
            )}
          </div>
          <Icon className={`w-8 h-8 ${color}`} />
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className={cn('space-y-6 p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => router.push('/admin')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Analytics & Reports</h1>
            <p className="text-muted-foreground">
              Comprehensive insights and performance metrics
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <MetricCard
          title="Total Users"
          value={mockMetrics.users.total}
          growth={mockMetrics.users.growth}
          icon={Users}
          color="text-blue-600"
          subtitle={`${mockMetrics.users.active} active`}
        />
        <MetricCard
          title="Companies"
          value={mockMetrics.companies.total}
          growth={mockMetrics.companies.growth}
          icon={Building}
          color="text-purple-600"
          subtitle={`${mockMetrics.companies.verified} verified`}
        />
        <MetricCard
          title="Active Jobs"
          value={mockMetrics.jobs.total}
          growth={mockMetrics.jobs.growth}
          icon={Briefcase}
          color="text-green-600"
          subtitle={`${mockMetrics.jobs.active} live`}
        />
        <MetricCard
          title="Applications"
          value={mockMetrics.applications.total}
          growth={mockMetrics.applications.growth}
          icon={FileText}
          color="text-orange-600"
          subtitle={`${mockMetrics.applications.successful} successful`}
        />
        <MetricCard
          title="Revenue"
          value={`$${(mockMetrics.revenue.total / 1000).toFixed(0)}k`}
          growth={mockMetrics.revenue.growth}
          icon={DollarSign}
          color="text-indigo-600"
          subtitle={`$${(mockMetrics.revenue.recurring / 1000).toFixed(0)}k recurring`}
        />
      </div>

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="companies">Companies</TabsTrigger>
          <TabsTrigger value="jobs">Jobs</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Growth Trends</CardTitle>
                <CardDescription>Key metrics over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <div className="text-center">
                    <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                    <p className="text-muted-foreground">Growth chart will be displayed here</p>
                    <p className="text-sm text-muted-foreground">Integration with charting library needed</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Platform Activity</CardTitle>
                <CardDescription>Real-time system activity</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Activity className="w-4 h-4 text-green-600" />
                      <span className="text-sm">System Health</span>
                    </div>
                    <span className="text-sm font-medium text-green-600">Excellent</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4 text-blue-600" />
                      <span className="text-sm">Active Users</span>
                    </div>
                    <span className="text-sm font-medium">1,247</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Briefcase className="w-4 h-4 text-purple-600" />
                      <span className="text-sm">Jobs Posted Today</span>
                    </div>
                    <span className="text-sm font-medium">23</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <FileText className="w-4 h-4 text-orange-600" />
                      <span className="text-sm">Applications Today</span>
                    </div>
                    <span className="text-sm font-medium">156</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest platform events and milestones</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-4 p-3 bg-muted/50 rounded-lg">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <Users className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">New user milestone reached</p>
                    <p className="text-xs text-muted-foreground">15,000+ registered users</p>
                  </div>
                  <div className="ml-auto text-xs text-muted-foreground">2 hours ago</div>
                </div>
                <div className="flex items-center space-x-4 p-3 bg-muted/50 rounded-lg">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Building className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Company verification completed</p>
                    <p className="text-xs text-muted-foreground">TechCorp Inc. verified successfully</p>
                  </div>
                  <div className="ml-auto text-xs text-muted-foreground">5 hours ago</div>
                </div>
                <div className="flex items-center space-x-4 p-3 bg-muted/50 rounded-lg">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <DollarSign className="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Revenue target achieved</p>
                    <p className="text-xs text-muted-foreground">Monthly recurring revenue: $98K</p>
                  </div>
                  <div className="ml-auto text-xs text-muted-foreground">1 day ago</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Analytics</CardTitle>
              <CardDescription>Detailed user metrics and behavior analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">User Analytics</h3>
                <p className="text-muted-foreground">
                  Detailed user analytics and segmentation will be displayed here
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  Implementation in progress...
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="companies" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Company Analytics</CardTitle>
              <CardDescription>Company performance and engagement metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Building className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Company Analytics</h3>
                <p className="text-muted-foreground">
                  Company metrics and performance analytics will be displayed here
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  Implementation in progress...
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="jobs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Job Analytics</CardTitle>
              <CardDescription>Job posting performance and application metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Briefcase className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Job Analytics</h3>
                <p className="text-muted-foreground">
                  Job performance metrics and application analytics will be displayed here
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  Implementation in progress...
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Analytics</CardTitle>
              <CardDescription>Financial performance and subscription metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <DollarSign className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Revenue Analytics</h3>
                <p className="text-muted-foreground">
                  Revenue tracking and subscription analytics will be displayed here
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  Implementation in progress...
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
