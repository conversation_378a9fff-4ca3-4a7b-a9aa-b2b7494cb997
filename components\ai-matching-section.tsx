"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Brain, Sparkles, Target, TrendingUp, Zap, Star } from "lucide-react"
import { PremiumJobDetailModal } from "@/components/jobs/premium-job-detail-modal"
import { JobApplicationModal } from "@/components/jobs/job-application-modal"

const aiMatches = [
  {
    id: 1,
    title: "Senior React Developer",
    company: "TechFlow Inc.",
    matchScore: 95,
    salary: "$140k - $180k",
    location: "San Francisco, CA",
    skills: ["React", "TypeScript", "Node.js"],
    reasons: ["Perfect skill match", "Salary expectations aligned", "Company culture fit"],
    logo: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 2,
    title: "Full Stack Engineer",
    company: "InnovateHub",
    matchScore: 88,
    salary: "$120k - $160k",
    location: "Remote",
    skills: ["JavaScript", "Python", "AWS"],
    reasons: ["Strong technical background", "Remote work preference", "Growth potential"],
    logo: "/placeholder.svg?height=60&width=60",
  },
  {
    id: 3,
    title: "Frontend Architect",
    company: "DesignTech",
    matchScore: 82,
    salary: "$130k - $170k",
    location: "New York, NY",
    skills: ["Vue.js", "CSS", "Design Systems"],
    reasons: ["Leadership experience", "Design background", "Architecture skills"],
    logo: "/placeholder.svg?height=60&width=60",
  },
]

export function AIMatchingSection() {
  const [currentMatch, setCurrentMatch] = useState(0)
  const [isAnalyzing, setIsAnalyzing] = useState(true)
  const [selectedJob, setSelectedJob] = useState<any>(null)
  const [applicationJob, setApplicationJob] = useState<any>(null)

  // Transform AI match data to job format
  const transformMatchToJob = (match: any) => ({
    ...match,
    company: {
      name: match.company,
      logo: match.logo,
      verified: true,
      id: match.id,
      industry: "Technology",
      size: "100-500",
      rating: 4.5
    },
    type: "Full-time",
    remote: match.location === "Remote",
    workModel: match.location === "Remote" ? "Remote" : "On-site",
    posted: "2 hours ago",
    applicants: Math.floor(Math.random() * 50) + 10,
    views: Math.floor(Math.random() * 200) + 50,
    featured: match.matchScore > 90,
    urgent: false,
    category: "Technology",
    description: `Join our team as a ${match.title}. We're looking for someone with ${match.skills.join(', ')} experience.`,
    requirements: match.skills,
    responsibilities: ["Develop and maintain applications", "Collaborate with team members", "Write clean, maintainable code"],
    benefits: ["Health insurance", "401k matching", "Flexible hours", "Remote work options"],
    experience: match.matchScore > 90 ? "Senior Level" : "Mid Level",
    salary: {
      min: parseInt(match.salary.replace(/[^0-9]/g, '').slice(0, 3)) * 1000,
      max: parseInt(match.salary.replace(/[^0-9]/g, '').slice(3, 6)) * 1000,
      currency: "USD",
      period: "year"
    }
  })

  useEffect(() => {
    const timer = setTimeout(() => setIsAnalyzing(false), 3000)
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentMatch((prev) => (prev + 1) % aiMatches.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [])

  return (
    <section className="py-20 bg-gradient-to-br from-primary/5 via-background to-primary/10">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
            <Brain className="w-4 h-4 mr-2" />
            AI-Powered Matching
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Let AI Find Your <span className="text-primary">Perfect Match</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Our advanced AI analyzes your skills, preferences, and career goals to find opportunities that truly fit.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* AI Analysis Visualization */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <Card className="glass">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Sparkles className="w-5 h-5 text-primary" />
                  <span>AI Analysis in Progress</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <AnimatePresence mode="wait">
                  {isAnalyzing ? (
                    <motion.div
                      key="analyzing"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="space-y-4"
                    >
                      {[
                        { label: "Analyzing Skills", progress: 100 },
                        { label: "Matching Companies", progress: 75 },
                        { label: "Calculating Fit Score", progress: 45 },
                      ].map((item, index) => (
                        <div key={item.label} className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>{item.label}</span>
                            <span>{item.progress}%</span>
                          </div>
                          <Progress value={item.progress} className="h-2" />
                        </div>
                      ))}
                    </motion.div>
                  ) : (
                    <motion.div
                      key="results"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="space-y-4"
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Profile Strength</span>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          Excellent
                        </Badge>
                      </div>
                      <Progress value={92} className="h-3" />
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <Target className="w-4 h-4 text-primary" />
                          <span>Skills Match: 95%</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <TrendingUp className="w-4 h-4 text-green-500" />
                          <span>Salary Fit: 88%</span>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </CardContent>
            </Card>

            <div className="grid grid-cols-3 gap-4">
              {[
                { icon: Zap, label: "Instant Matching", value: "< 1s" },
                { icon: Target, label: "Accuracy Rate", value: "94%" },
                { icon: Star, label: "Success Rate", value: "87%" },
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  className="text-center p-4 rounded-xl bg-background/80 backdrop-blur-sm border border-border/50"
                >
                  <stat.icon className="w-8 h-8 text-primary mx-auto mb-2" />
                  <div className="text-2xl font-bold text-primary">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* AI Match Results */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="relative"
          >
            <AnimatePresence mode="wait">
              <motion.div
                key={currentMatch}
                initial={{ opacity: 0, rotateY: 90 }}
                animate={{ opacity: 1, rotateY: 0 }}
                exit={{ opacity: 0, rotateY: -90 }}
                transition={{ duration: 0.6 }}
                className="perspective-1000"
              >
                <Card className="glass hover:shadow-2xl transition-all duration-300 border-primary/20">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 rounded-xl bg-muted flex items-center justify-center">
                          <img
                            src={aiMatches[currentMatch].logo || "/placeholder.svg"}
                            alt="Company logo"
                            className="w-full h-full object-cover rounded-xl"
                          />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold">{aiMatches[currentMatch].title}</h3>
                          <p className="text-muted-foreground">{aiMatches[currentMatch].company}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-3xl font-bold text-primary">{aiMatches[currentMatch].matchScore}%</div>
                        <div className="text-sm text-muted-foreground">Match Score</div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>AI Compatibility</span>
                        <span>{aiMatches[currentMatch].matchScore}%</span>
                      </div>
                      <Progress value={aiMatches[currentMatch].matchScore} className="h-2" />
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Salary:</span>
                        <div className="font-semibold">{aiMatches[currentMatch].salary}</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Location:</span>
                        <div className="font-semibold">{aiMatches[currentMatch].location}</div>
                      </div>
                    </div>

                    <div>
                      <span className="text-sm text-muted-foreground mb-2 block">Required Skills:</span>
                      <div className="flex flex-wrap gap-2">
                        {aiMatches[currentMatch].skills.map((skill) => (
                          <Badge key={skill} variant="secondary">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <span className="text-sm text-muted-foreground mb-2 block">Why it's a great match:</span>
                      <ul className="space-y-1">
                        {aiMatches[currentMatch].reasons.map((reason, index) => (
                          <li key={index} className="text-sm flex items-center space-x-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                            <span>{reason}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="flex space-x-3">
                      <Button
                        className="flex-1"
                        onClick={() => setApplicationJob(transformMatchToJob(match))}
                      >
                        Apply Now
                      </Button>
                      <Button
                        variant="outline"
                        className="flex-1 bg-transparent"
                        onClick={() => setSelectedJob(transformMatchToJob(match))}
                      >
                        Learn More
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </AnimatePresence>

            {/* Match indicators */}
            <div className="flex justify-center space-x-2 mt-6">
              {aiMatches.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentMatch(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentMatch ? "bg-primary scale-125" : "bg-muted-foreground/30"
                  }`}
                />
              ))}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Job Detail Modal */}
      <PremiumJobDetailModal
        job={selectedJob}
        isOpen={!!selectedJob}
        onClose={() => setSelectedJob(null)}
        onApply={(job) => {
          setApplicationJob(job)
          setSelectedJob(null)
        }}
        onSave={(job) => {
          console.log('Save job:', job.title)
        }}
      />

      {/* Job Application Modal */}
      <JobApplicationModal
        job={applicationJob}
        isOpen={!!applicationJob}
        onClose={() => setApplicationJob(null)}
        onSubmit={(applicationData) => {
          console.log('Application submitted:', applicationData)
          alert(`Application submitted successfully for ${applicationData.jobTitle}!`)
          setApplicationJob(null)
        }}
      />
    </section>
  )
}
