'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useApplicationsStore, type Application, type ApplicationFilters } from '@/stores'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ApplicationCard } from './application-card'
import { ApplicationStats } from './application-stats'
import { PageLoader } from '@/components/ui/page-loader'
import { ErrorAlert } from '@/components/ui/error-alert'
import { 
  Search, 
  Filter, 
  Calendar,
  Briefcase,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ApplicationsDashboardProps {
  className?: string
}

export function ApplicationsDashboard({ className }: ApplicationsDashboardProps) {
  const router = useRouter()
  const {
    applications,
    stats,
    filters,
    applicationsLoading,
    statsLoading,
    applicationsError,
    pagination,
    getApplications,
    getApplicationStats,
    updateFilters,
    clearFilters,
    clearApplicationsError
  } = useApplicationsStore()

  const [searchQuery, setSearchQuery] = useState('')
  const [activeTab, setActiveTab] = useState('all')

  // Load data on mount
  useEffect(() => {
    getApplications()
    getApplicationStats()
  }, [getApplications, getApplicationStats])

  // Filter applications by tab
  const getFilteredApplications = () => {
    let filtered = applications

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(app =>
        app.job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.job.company.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Filter by tab
    switch (activeTab) {
      case 'pending':
        return filtered.filter(app => ['submitted', 'under_review'].includes(app.status))
      case 'interviews':
        return filtered.filter(app => ['interview_scheduled', 'interviewed'].includes(app.status))
      case 'offers':
        return filtered.filter(app => app.status === 'offer_extended')
      case 'rejected':
        return filtered.filter(app => app.status === 'rejected')
      case 'withdrawn':
        return filtered.filter(app => app.status === 'withdrawn')
      default:
        return filtered
    }
  }

  const filteredApplications = getFilteredApplications()

  // Status filter options
  const statusOptions = [
    { value: 'all', label: 'All Applications', count: applications.length },
    { value: 'pending', label: 'Pending Review', count: applications.filter(app => ['submitted', 'under_review'].includes(app.status)).length },
    { value: 'interviews', label: 'Interviews', count: applications.filter(app => ['interview_scheduled', 'interviewed'].includes(app.status)).length },
    { value: 'offers', label: 'Offers', count: applications.filter(app => app.status === 'offer_extended').length },
    { value: 'rejected', label: 'Rejected', count: applications.filter(app => app.status === 'rejected').length },
    { value: 'withdrawn', label: 'Withdrawn', count: applications.filter(app => app.status === 'withdrawn').length }
  ]

  // Handle filter changes
  const handleStatusFilter = (status: string) => {
    const statusFilters: Application['status'][] = []
    
    switch (status) {
      case 'pending':
        statusFilters.push('submitted', 'under_review')
        break
      case 'interviews':
        statusFilters.push('interview_scheduled', 'interviewed')
        break
      case 'offers':
        statusFilters.push('offer_extended')
        break
      case 'rejected':
        statusFilters.push('rejected')
        break
      case 'withdrawn':
        statusFilters.push('withdrawn')
        break
      default:
        // All applications - no filter
        break
    }

    updateFilters({ status: statusFilters })
    getApplications()
  }

  // Get status icon
  const getStatusIcon = (status: Application['status']) => {
    switch (status) {
      case 'submitted':
      case 'under_review':
        return <Clock className="w-4 h-4 text-yellow-600" />
      case 'interview_scheduled':
      case 'interviewed':
        return <Calendar className="w-4 h-4 text-blue-600" />
      case 'offer_extended':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-600" />
      case 'withdrawn':
        return <AlertCircle className="w-4 h-4 text-gray-600" />
      default:
        return <Briefcase className="w-4 h-4 text-muted-foreground" />
    }
  }

  // Loading state
  if (applicationsLoading && !applications.length) {
    return <PageLoader message="Loading your applications..." />
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">My Applications</h1>
          <p className="text-muted-foreground">
            Track and manage your job applications
          </p>
        </div>
        <Button onClick={() => router.push('/jobs')}>
          <Briefcase className="w-4 h-4 mr-2" />
          Find More Jobs
        </Button>
      </div>

      {/* Stats */}
      {stats && !statsLoading && (
        <ApplicationStats stats={stats} />
      )}

      {/* Error Alert */}
      {applicationsError && (
        <ErrorAlert
          type="error"
          title="Failed to Load Applications"
          message={applicationsError.message}
          dismissible
          onDismiss={clearApplicationsError}
          actions={
            <Button
              variant="outline"
              size="sm"
              onClick={() => getApplications()}
              className="mt-2"
            >
              Try Again
            </Button>
          }
        />
      )}

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Filter className="w-5 h-5" />
              <span>Filter Applications</span>
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                clearFilters()
                setSearchQuery('')
                setActiveTab('all')
                getApplications()
              }}
            >
              Clear Filters
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <Label htmlFor="search">Search Applications</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  id="search"
                  placeholder="Search by job title or company..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Date Range Filter */}
            <div className="md:w-48">
              <Label>Date Range</Label>
              <Select defaultValue="all">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 3 months</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Applications Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          {statusOptions.map((option) => (
            <TabsTrigger
              key={option.value}
              value={option.value}
              className="flex items-center space-x-2"
            >
              <span>{option.label}</span>
              {option.count > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {option.count}
                </Badge>
              )}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          {filteredApplications.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Briefcase className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {searchQuery ? 'No matching applications' : 'No applications found'}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery 
                    ? 'Try adjusting your search criteria'
                    : activeTab === 'all' 
                      ? "You haven't applied to any jobs yet"
                      : `No applications with ${activeTab} status`
                  }
                </p>
                {activeTab === 'all' && !searchQuery && (
                  <Button onClick={() => router.push('/jobs')}>
                    Start Applying to Jobs
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredApplications.map((application) => (
                <ApplicationCard
                  key={application._id}
                  application={application}
                  onView={() => router.push(`/applications/${application._id}`)}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            disabled={pagination.page <= 1}
            onClick={() => {
              // Handle previous page
            }}
          >
            Previous
          </Button>
          <span className="text-sm text-muted-foreground">
            Page {pagination.page} of {pagination.totalPages}
          </span>
          <Button
            variant="outline"
            disabled={pagination.page >= pagination.totalPages}
            onClick={() => {
              // Handle next page
            }}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}
