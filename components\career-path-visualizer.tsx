"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { DollarSign, Clock, ArrowRight, Target, Lightbulb, BookOpen, Award } from "lucide-react"

const careerPaths = {
  frontend: {
    title: "Frontend Development",
    icon: "💻",
    color: "bg-blue-500",
    levels: [
      {
        title: "Junior Frontend Developer",
        salary: "$60k - $80k",
        experience: "0-2 years",
        skills: ["HTML", "CSS", "JavaScript", "React Basics"],
        responsibilities: ["Build UI components", "Fix bugs", "Learn from seniors"],
        companies: ["Startups", "Small agencies"],
        timeToNext: "12-18 months",
      },
      {
        title: "Frontend Developer",
        salary: "$80k - $110k",
        experience: "2-4 years",
        skills: ["React/Vue", "TypeScript", "State Management", "Testing"],
        responsibilities: ["Feature development", "Code reviews", "Mentoring juniors"],
        companies: ["Mid-size companies", "Product teams"],
        timeToNext: "18-24 months",
      },
      {
        title: "Senior Frontend Developer",
        salary: "$110k - $150k",
        experience: "4-7 years",
        skills: ["Architecture", "Performance", "Advanced React", "Team Leadership"],
        responsibilities: ["Technical decisions", "System design", "Team leadership"],
        companies: ["Large tech companies", "Scale-ups"],
        timeToNext: "24-36 months",
      },
      {
        title: "Frontend Architect / Engineering Manager",
        salary: "$150k - $200k+",
        experience: "7+ years",
        skills: ["System Architecture", "Team Management", "Strategy", "Cross-team collaboration"],
        responsibilities: ["Technical strategy", "Team building", "Architecture decisions"],
        companies: ["FAANG", "Unicorns", "Enterprise"],
        timeToNext: "Career peak",
      },
    ],
  },
  product: {
    title: "Product Management",
    icon: "📊",
    color: "bg-green-500",
    levels: [
      {
        title: "Associate Product Manager",
        salary: "$70k - $95k",
        experience: "0-2 years",
        skills: ["Analytics", "User Research", "Basic PM Tools", "Communication"],
        responsibilities: ["Feature specs", "User research", "Data analysis"],
        companies: ["Startups", "Product companies"],
        timeToNext: "12-18 months",
      },
      {
        title: "Product Manager",
        salary: "$95k - $130k",
        experience: "2-5 years",
        skills: ["Strategy", "Roadmapping", "A/B Testing", "Stakeholder Management"],
        responsibilities: ["Product strategy", "Roadmap planning", "Cross-team coordination"],
        companies: ["Scale-ups", "Mid-size tech"],
        timeToNext: "18-30 months",
      },
      {
        title: "Senior Product Manager",
        salary: "$130k - $170k",
        experience: "5-8 years",
        skills: ["Product Strategy", "Market Analysis", "Team Leadership", "Business Metrics"],
        responsibilities: ["Product vision", "Team leadership", "Business impact"],
        companies: ["Large tech", "Public companies"],
        timeToNext: "24-36 months",
      },
      {
        title: "Director of Product / VP Product",
        salary: "$170k - $250k+",
        experience: "8+ years",
        skills: ["Organizational Leadership", "Business Strategy", "P&L Management"],
        responsibilities: ["Product organization", "Business strategy", "Executive decisions"],
        companies: ["FAANG", "Unicorns", "Fortune 500"],
        timeToNext: "Career peak",
      },
    ],
  },
}

const skillRecommendations = {
  frontend: [
    { skill: "Advanced React Patterns", priority: "High", timeToLearn: "2-3 months" },
    { skill: "System Design", priority: "High", timeToLearn: "3-6 months" },
    { skill: "Performance Optimization", priority: "Medium", timeToLearn: "1-2 months" },
    { skill: "Testing Strategies", priority: "Medium", timeToLearn: "1-2 months" },
  ],
  product: [
    { skill: "Data Analysis", priority: "High", timeToLearn: "2-3 months" },
    { skill: "User Research Methods", priority: "High", timeToLearn: "1-2 months" },
    { skill: "Business Strategy", priority: "Medium", timeToLearn: "3-4 months" },
    { skill: "Technical Understanding", priority: "Medium", timeToLearn: "2-3 months" },
  ],
}

export function CareerPathVisualizer() {
  const [selectedPath, setSelectedPath] = useState<keyof typeof careerPaths>("frontend")
  const [currentLevel, setCurrentLevel] = useState(1)

  const pathData = careerPaths[selectedPath]
  const currentLevelData = pathData.levels[currentLevel]
  const nextLevelData = pathData.levels[currentLevel + 1]

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
            <Target className="w-4 h-4 mr-2" />
            Career Path Visualizer
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Plan Your <span className="text-primary">Career Journey</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Visualize your career progression with detailed roadmaps, salary expectations, and skill requirements.
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto">
          {/* Path Selection */}
          <div className="flex justify-center mb-12">
            <div className="flex space-x-4 p-2 bg-muted/50 rounded-lg">
              {Object.entries(careerPaths).map(([key, path]) => (
                <Button
                  key={key}
                  variant={selectedPath === key ? "default" : "ghost"}
                  onClick={() => setSelectedPath(key as keyof typeof careerPaths)}
                  className="flex items-center space-x-2"
                >
                  <span className="text-lg">{path.icon}</span>
                  <span>{path.title}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Career Path Timeline */}
          <motion.div
            key={selectedPath}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-12"
          >
            <div className="relative">
              {/* Timeline Line */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary via-primary/50 to-muted" />

              <div className="space-y-8">
                {pathData.levels.map((level, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="relative flex items-start space-x-8"
                  >
                    {/* Timeline Dot */}
                    <div
                      className={`relative z-10 w-16 h-16 rounded-full flex items-center justify-center ${
                        index <= currentLevel ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                      } border-4 border-background shadow-lg`}
                    >
                      <span className="font-bold">{index + 1}</span>
                    </div>

                    {/* Level Card */}
                    <Card
                      className={`flex-1 glass transition-all duration-300 ${
                        index === currentLevel ? "border-primary/50 bg-primary/5" : "border-border/50"
                      }`}
                    >
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-xl">{level.title}</CardTitle>
                          <div className="flex items-center space-x-2">
                            <Badge variant={index <= currentLevel ? "default" : "secondary"}>{level.experience}</Badge>
                            {index === currentLevel && <Badge className="bg-green-100 text-green-800">Current</Badge>}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2 text-sm">
                              <DollarSign className="w-4 h-4 text-primary" />
                              <span className="font-semibold">{level.salary}</span>
                            </div>
                            <div className="flex items-center space-x-2 text-sm">
                              <Clock className="w-4 h-4 text-muted-foreground" />
                              <span>Time to next: {level.timeToNext}</span>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <div className="text-sm">
                              <span className="font-medium">Companies: </span>
                              <span className="text-muted-foreground">{level.companies.join(", ")}</span>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div>
                            <h4 className="font-medium text-sm mb-2">Key Skills:</h4>
                            <div className="flex flex-wrap gap-2">
                              {level.skills.map((skill) => (
                                <Badge key={skill} variant="outline" className="text-xs">
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div>
                            <h4 className="font-medium text-sm mb-2">Responsibilities:</h4>
                            <ul className="text-sm text-muted-foreground space-y-1">
                              {level.responsibilities.map((resp, idx) => (
                                <li key={idx} className="flex items-start space-x-2">
                                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                                  <span>{resp}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>

                        {index === currentLevel && nextLevelData && (
                          <div className="pt-4 border-t border-border/50">
                            <Button
                              size="sm"
                              onClick={() => setCurrentLevel(Math.min(currentLevel + 1, pathData.levels.length - 1))}
                              className="w-full"
                            >
                              Progress to {nextLevelData.title}
                              <ArrowRight className="w-4 h-4 ml-2" />
                            </Button>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Skill Development Recommendations */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <Card className="glass h-full">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Lightbulb className="w-5 h-5 text-primary" />
                    <span>Skill Development Plan</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {skillRecommendations[selectedPath].map((skill, index) => (
                    <div key={skill.skill} className="p-4 rounded-lg bg-muted/30 space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold">{skill.skill}</h4>
                        <Badge variant={skill.priority === "High" ? "destructive" : "secondary"}>
                          {skill.priority}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <span>Time to learn: {skill.timeToLearn}</span>
                        <Button size="sm" variant="outline">
                          <BookOpen className="w-3 h-3 mr-1" />
                          Learn
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="glass h-full">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Award className="w-5 h-5 text-primary" />
                    <span>Career Insights</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="p-4 rounded-lg bg-primary/5 border border-primary/20">
                      <h4 className="font-semibold text-primary mb-2">Salary Growth Potential</h4>
                      <div className="text-2xl font-bold mb-1">
                        {Math.round(
                          ((Number.parseInt(
                            pathData.levels[pathData.levels.length - 1].salary.split("$")[1].split("k")[0],
                          ) -
                            Number.parseInt(pathData.levels[0].salary.split("$")[1].split("k")[0])) /
                            Number.parseInt(pathData.levels[0].salary.split("$")[1].split("k")[0])) *
                            100,
                        )}
                        %
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Average salary increase from entry to senior level
                      </p>
                    </div>

                    <div className="p-4 rounded-lg bg-muted/30">
                      <h4 className="font-semibold mb-2">Market Demand</h4>
                      <Progress value={selectedPath === "frontend" ? 92 : 87} className="h-2 mb-2" />
                      <p className="text-sm text-muted-foreground">
                        {selectedPath === "frontend" ? "92%" : "87%"} of companies are actively hiring
                      </p>
                    </div>

                    <div className="p-4 rounded-lg bg-muted/30">
                      <h4 className="font-semibold mb-2">Remote Opportunities</h4>
                      <Progress value={selectedPath === "frontend" ? 78 : 65} className="h-2 mb-2" />
                      <p className="text-sm text-muted-foreground">
                        {selectedPath === "frontend" ? "78%" : "65%"} of positions offer remote work
                      </p>
                    </div>
                  </div>

                  <Button className="w-full">Get Personalized Career Plan</Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}
