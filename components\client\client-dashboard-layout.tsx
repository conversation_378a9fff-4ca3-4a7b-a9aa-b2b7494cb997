"use client"

import React from "react"
import { ClientDashboardTopbar } from "@/components/client/client-dashboard-topbar"
import { ClientDashboardSidebar } from "@/components/client/client-dashboard-sidebar"
import { BackgroundPattern } from "@/components/background-pattern"
import { NotificationSystem } from "@/components/notification-system"
import { SidebarProvider } from "@/components/ui/sidebar"

interface ClientDashboardLayoutProps {
  children: React.ReactNode
  showBackgroundPattern?: boolean
  showNotificationSystem?: boolean
  className?: string
}

export function ClientDashboardLayout({
  children,
  showBackgroundPattern = true,
  showNotificationSystem = true,
  className = "min-h-screen bg-background"
}: ClientDashboardLayoutProps) {
  return (
    <SidebarProvider>
      <div className={className}>
        {showBackgroundPattern && <BackgroundPattern />}
        
        {/* Client Dashboard Layout Structure */}
        <div className="flex h-screen overflow-hidden">
          {/* Sidebar */}
          <ClientDashboardSidebar />

          {/* Main Content Area */}
          <div className="flex-1 flex flex-col overflow-hidden min-w-0">
            {/* Top Navigation */}
            <ClientDashboardTopbar />

            {/* Main Content */}
            <main className="flex-1 overflow-auto relative">
              <div className="h-full w-full p-3 pr-1 space-y-6">
                {children}
              </div>
            </main>
          </div>
        </div>
        
        {showNotificationSystem && <NotificationSystem />}
      </div>
    </SidebarProvider>
  )
}
