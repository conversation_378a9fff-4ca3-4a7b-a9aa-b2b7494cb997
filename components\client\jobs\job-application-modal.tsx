"use client"

import React, { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useClientStore } from "@/stores/client.store"
import { useClientApplicationsStore } from "@/stores/client-applications.store"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  Briefcase, 
  FileText, 
  Upload,
  MapPin,
  DollarSign,
  Clock,
  Building2,
  Send,
  Loader2,
  CheckCircle,
  AlertCircle
} from "lucide-react"

interface Job {
  id: string
  title: string
  company: string
  location: string
  salary?: string
  type: string
  description: string
  requirements: string[]
  remote: boolean
  urgent: boolean
  featured: boolean
}

interface JobApplicationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  job: Job | null
}

export function JobApplicationModal({ open, onOpenChange, job }: JobApplicationModalProps) {
  const { client } = useClientStore()
  const { applyToJob, submitting } = useClientApplicationsStore()
  
  const [applicationData, setApplicationData] = useState({
    coverLetter: "",
    resumeId: "",
    additionalDocuments: [] as string[],
    customAnswers: {} as Record<string, string>
  })
  
  const [step, setStep] = useState(1) // 1: Review, 2: Documents, 3: Cover Letter, 4: Submit
  const [submitted, setSubmitted] = useState(false)

  const handleSubmit = async () => {
    if (!job) return

    try {
      await applyToJob(job.id, {
        ...applicationData,
        source: 'job_portal'
      })
      setSubmitted(true)
      setTimeout(() => {
        onOpenChange(false)
        setSubmitted(false)
        setStep(1)
        setApplicationData({
          coverLetter: "",
          resumeId: "",
          additionalDocuments: [],
          customAnswers: {}
        })
      }, 2000)
    } catch (error) {
      console.error('Application submission failed:', error)
    }
  }

  if (!job) return null

  if (submitted) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-md">
          <div className="text-center py-8">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">Application Submitted!</h3>
            <p className="text-muted-foreground">
              Your application for {job.title} at {job.company} has been submitted successfully.
            </p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Briefcase className="w-5 h-5 mr-2" />
            Apply for {job.title}
          </DialogTitle>
          <DialogDescription>
            {job.company} • {job.location}
          </DialogDescription>
        </DialogHeader>

        {/* Progress Steps */}
        <div className="flex items-center justify-between mb-6">
          {[1, 2, 3, 4].map((stepNum) => (
            <div key={stepNum} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                step >= stepNum 
                  ? 'bg-primary text-primary-foreground' 
                  : 'bg-muted text-muted-foreground'
              }`}>
                {stepNum}
              </div>
              {stepNum < 4 && (
                <div className={`w-16 h-0.5 mx-2 ${
                  step > stepNum ? 'bg-primary' : 'bg-muted'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        {step === 1 && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Job Details</CardTitle>
                <CardDescription>Review the position you're applying for</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">{job.title}</h3>
                    <p className="text-muted-foreground">{job.company}</p>
                  </div>
                  <div className="flex space-x-2">
                    {job.featured && <Badge>Featured</Badge>}
                    {job.urgent && <Badge variant="destructive">Urgent</Badge>}
                    {job.remote && <Badge variant="secondary">Remote</Badge>}
                  </div>
                </div>

                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {job.location}
                  </div>
                  <div className="flex items-center">
                    <Briefcase className="w-4 h-4 mr-1" />
                    {job.type}
                  </div>
                  {job.salary && (
                    <div className="flex items-center">
                      <DollarSign className="w-4 h-4 mr-1" />
                      {job.salary}
                    </div>
                  )}
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium mb-2">Job Description</h4>
                  <p className="text-sm text-muted-foreground">{job.description}</p>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Requirements</h4>
                  <div className="flex flex-wrap gap-1">
                    {job.requirements.map((req, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {req}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Your Profile</CardTitle>
                <CardDescription>Make sure your profile is up to date</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Briefcase className="w-6 h-6 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">{client?.fullName}</h4>
                    <p className="text-sm text-muted-foreground">{client?.headline}</p>
                    <div className="flex items-center mt-1">
                      <span className="text-xs text-muted-foreground">Profile Completeness: </span>
                      <span className="text-xs font-medium ml-1">{client?.profileCompleteness}%</span>
                    </div>
                  </div>
                  {(client?.profileCompleteness || 0) < 80 && (
                    <div className="flex items-center text-amber-600">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      <span className="text-xs">Incomplete</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {step === 2 && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Select Documents</CardTitle>
                <CardDescription>Choose which documents to include with your application</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Resume</Label>
                  <Select 
                    value={applicationData.resumeId} 
                    onValueChange={(value) => setApplicationData({
                      ...applicationData,
                      resumeId: value
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select your resume" />
                    </SelectTrigger>
                    <SelectContent>
                      {client?.documents?.resume?.map((resume, index) => (
                        <SelectItem key={index} value={resume.url}>
                          {resume.filename}
                          {resume.isPrimary && <Badge className="ml-2 text-xs">Primary</Badge>}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {!client?.documents?.resume?.length && (
                  <div className="text-center py-6 border-2 border-dashed rounded-lg">
                    <FileText className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No resume uploaded</p>
                    <Button variant="outline" size="sm" className="mt-2">
                      <Upload className="w-4 h-4 mr-2" />
                      Upload Resume
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {step === 3 && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Cover Letter</CardTitle>
                <CardDescription>Write a personalized cover letter for this position</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder="Dear Hiring Manager,

I am writing to express my interest in the [Position Title] role at [Company Name]. With my background in [relevant experience], I am confident that I would be a valuable addition to your team.

[Customize this section with specific examples of your experience and achievements that relate to the job requirements]

I am excited about the opportunity to contribute to [Company Name] and would welcome the chance to discuss how my skills and experience align with your needs.

Thank you for your consideration.

Best regards,
[Your Name]"
                  rows={12}
                  value={applicationData.coverLetter}
                  onChange={(e) => setApplicationData({
                    ...applicationData,
                    coverLetter: e.target.value
                  })}
                />
                <div className="flex justify-between items-center mt-2">
                  <span className="text-xs text-muted-foreground">
                    {applicationData.coverLetter.length} characters
                  </span>
                  <Button variant="outline" size="sm">
                    Use Template
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {step === 4 && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Review Application</CardTitle>
                <CardDescription>Review your application before submitting</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium">Position</h4>
                  <p className="text-sm text-muted-foreground">{job.title} at {job.company}</p>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium">Resume</h4>
                  <p className="text-sm text-muted-foreground">
                    {applicationData.resumeId ? 'Resume selected' : 'No resume selected'}
                  </p>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium">Cover Letter</h4>
                  <p className="text-sm text-muted-foreground">
                    {applicationData.coverLetter.length > 0 
                      ? `${applicationData.coverLetter.length} characters` 
                      : 'No cover letter'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Navigation */}
        <div className="flex justify-between pt-4 border-t">
          <Button 
            variant="outline" 
            onClick={() => step > 1 ? setStep(step - 1) : onOpenChange(false)}
          >
            {step === 1 ? 'Cancel' : 'Back'}
          </Button>
          
          {step < 4 ? (
            <Button onClick={() => setStep(step + 1)}>
              Next
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={submitting}>
              {submitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Submit Application
                </>
              )}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
