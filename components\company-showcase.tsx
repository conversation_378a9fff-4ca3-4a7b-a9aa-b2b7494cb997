// "use client"

// import { motion } from "framer-motion"
// import { <PERSON><PERSON> } from "@/components/ui/button"
// import { Card, CardContent } from "@/components/ui/card"
// import { Badge } from "@/components/ui/badge"
// import { Building2, Users, MapPin, ExternalLink } from "lucide-react"
// import Image from "next/image"

// const companies = [
//   {
//     id: 1,
//     name: "TechCorp Inc.",
//     logo: "/images/companies/techcorp-logo.jpg",
//     industry: "Technology",
//     size: "1000+ employees",
//     location: "San Francisco, CA",
//     openJobs: 15,
//     description: "Leading technology company focused on innovative solutions.",
//     featured: true,
//   },
//   {
//     id: 2,
//     name: "InnovateLab",
//     logo: "/images/companies/innovatelab-logo.jpg",
//     industry: "Startup",
//     size: "50-200 employees",
//     location: "Austin, TX",
//     openJobs: 8,
//     description: "Fast-growing startup revolutionizing the industry.",
//     featured: false,
//   },
//   {
//     id: 3,
//     name: "DesignStudio",
//     logo: "/images/companies/globaltech-logo.jpg",
//     industry: "Design",
//     size: "100-500 employees",
//     location: "New York, NY",
//     openJobs: 12,
//     description: "Creative agency with award-winning design solutions.",
//     featured: true,
//   },
//   {
//     id: 4,
//     name: "CloudTech",
//     logo: "/images/hero/office-meeting.jpg",
//     industry: "Cloud Services",
//     size: "500-1000 employees",
//     location: "Seattle, WA",
//     openJobs: 20,
//     description: "Cloud infrastructure and services provider.",
//     featured: false,
//   },
// ]

// export function CompanyShowcase() {
//   const containerVariants = {
//     hidden: { opacity: 0 },
//     visible: {
//       opacity: 1,
//       transition: {
//         staggerChildren: 0.15,
//       },
//     },
//   }

//   const itemVariants = {
//     hidden: { opacity: 0, y: 30 },
//     visible: {
//       opacity: 1,
//       y: 0,
//       transition: {
//         duration: 0.6,
//         ease: "easeOut",
//       },
//     },
//   }

//   return (
//     <section className="py-20">
//       <div className="container mx-auto px-4">
//         <motion.div
//           initial={{ opacity: 0, y: 30 }}
//           whileInView={{ opacity: 1, y: 0 }}
//           viewport={{ once: true }}
//           transition={{ duration: 0.6 }}
//           className="text-center mb-16"
//         >
//           <h2 className="text-4xl md:text-5xl font-bold mb-6">
//             Top <span className="text-primary">Companies</span> Hiring
//           </h2>
//           <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
//             Join industry leaders and innovative startups that are shaping the future of work.
//           </p>
//         </motion.div>

//         <motion.div
//           variants={containerVariants}
//           initial="hidden"
//           whileInView="visible"
//           viewport={{ once: true }}
//           className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12"
//         >
//           {companies.map((company, index) => (
//             <motion.div key={company.id} variants={itemVariants}>
//               <Card className="group hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20 bg-background/80 backdrop-blur-sm relative overflow-hidden h-full">
//                 {company.featured && (
//                   <div className="absolute top-4 right-4 z-10">
//                     <Badge className="bg-primary/10 text-primary border-primary/20">Featured</Badge>
//                   </div>
//                 )}

//                 <CardContent className="p-8">
//                   <div className="flex items-start space-x-6 mb-6">
//                     <motion.div
//                       whileHover={{ scale: 1.1 }}
//                       className="w-20 h-20 rounded-2xl bg-muted flex items-center justify-center overflow-hidden flex-shrink-0"
//                     >
//                       <Image
//                         src={company.logo || "/placeholder.svg"}
//                         alt={`${company.name} logo`}
//                         width={80}
//                         height={80}
//                         className="w-full h-full object-cover"
//                       />
//                     </motion.div>
//                     <div className="flex-1">
//                       <h3 className="text-2xl font-bold mb-2 group-hover:text-primary transition-colors duration-200">
//                         {company.name}
//                       </h3>
//                       <p className="text-muted-foreground mb-4">{company.description}</p>
//                       <div className="space-y-2">
//                         <div className="flex items-center space-x-2 text-sm text-muted-foreground">
//                           <Building2 className="w-4 h-4" />
//                           <span>{company.industry}</span>
//                         </div>
//                         <div className="flex items-center space-x-2 text-sm text-muted-foreground">
//                           <Users className="w-4 h-4" />
//                           <span>{company.size}</span>
//                         </div>
//                         <div className="flex items-center space-x-2 text-sm text-muted-foreground">
//                           <MapPin className="w-4 h-4" />
//                           <span>{company.location}</span>
//                         </div>
//                       </div>
//                     </div>
//                   </div>

//                   <div className="flex items-center justify-between pt-6 border-t border-border/50">
//                     <div className="text-sm">
//                       <span className="font-semibold text-primary">{company.openJobs}</span>
//                       <span className="text-muted-foreground ml-1">open positions</span>
//                     </div>
//                     <Button
//                       variant="outline"
//                       size="sm"
//                       className="group-hover:scale-105 transition-transform duration-200 bg-transparent"
//                     >
//                       View Jobs
//                       <ExternalLink className="w-4 h-4 ml-2" />
//                     </Button>
//                   </div>
//                 </CardContent>
//               </Card>
//             </motion.div>
//           ))}
//         </motion.div>

//         <motion.div
//           initial={{ opacity: 0, y: 20 }}
//           whileInView={{ opacity: 1, y: 0 }}
//           viewport={{ once: true }}
//           transition={{ duration: 0.6, delay: 0.3 }}
//           className="text-center"
//         >
//           <Button size="lg" variant="outline" className="text-lg px-8 bg-transparent">
//             Explore All Companies
//             <ExternalLink className="w-5 h-5 ml-2" />
//           </Button>
//         </motion.div>
//       </div>
//     </section>
//   )
// }


"use client"

import { motion, Variants } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Building2, Users, MapPin, ExternalLink } from "lucide-react"
import Image from "next/image"

const companies = [
  {
    id: 1,
    name: "TechCorp Inc.",
    logo: "/images/companies/techcorp-logo.jpg",
    industry: "Technology",
    size: "1000+ employees",
    location: "San Francisco, CA",
    openJobs: 15,
    description: "Leading technology company focused on innovative solutions.",
    featured: true,
  },
  {
    id: 2,
    name: "InnovateLab",
    logo: "/images/companies/innovatelab-logo.jpg",
    industry: "Startup",
    size: "50-200 employees",
    location: "Austin, TX",
    openJobs: 8,
    description: "Fast-growing startup revolutionizing the industry.",
    featured: false,
  },
  {
    id: 3,
    name: "DesignStudio",
    logo: "/images/companies/globaltech-logo.jpg",
    industry: "Design",
    size: "100-500 employees",
    location: "New York, NY",
    openJobs: 12,
    description: "Creative agency with award-winning design solutions.",
    featured: true,
  },
  {
    id: 4,
    name: "CloudTech",
    logo: "/images/hero/office-meeting.jpg",
    industry: "Cloud Services",
    size: "500-1000 employees",
    location: "Seattle, WA",
    openJobs: 20,
    description: "Cloud infrastructure and services provider.",
    featured: false,
  },
]

const containerVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.15 },
  },
}

const cardVariants: Variants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut" as const,
    },
  },
}

export function CompanyShowcase() {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        
        {/* Section Heading */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Top <span className="text-primary">Companies</span> Hiring
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Join industry leaders and innovative startups that are shaping the future of work.
          </p>
        </motion.div>

        {/* Companies Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12"
        >
          {companies.map((company) => (
            <motion.div key={company.id} variants={cardVariants}>
              <Card className="group hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20 bg-background/80 backdrop-blur-sm relative overflow-hidden h-full">
                
                {company.featured && (
                  <div className="absolute top-4 right-4 z-10">
                    <Badge className="bg-primary/10 text-primary border-primary/20">Featured</Badge>
                  </div>
                )}

                <CardContent className="p-8">
                  <div className="flex items-start space-x-6 mb-6">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      className="w-20 h-20 rounded-2xl bg-muted flex items-center justify-center overflow-hidden flex-shrink-0"
                    >
                      <Image
                        src={company.logo || "/placeholder.svg"}
                        alt={`${company.name} logo`}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover"
                      />
                    </motion.div>

                    <div className="flex-1">
                      <h3 className="text-2xl font-bold mb-2 group-hover:text-primary transition-colors duration-200">
                        {company.name}
                      </h3>
                      <p className="text-muted-foreground mb-4">{company.description}</p>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <Building2 className="w-4 h-4" />
                          <span>{company.industry}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <Users className="w-4 h-4" />
                          <span>{company.size}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <MapPin className="w-4 h-4" />
                          <span>{company.location}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-6 border-t border-border/50">
                    <div className="text-sm">
                      <span className="font-semibold text-primary">{company.openJobs}</span>
                      <span className="text-muted-foreground ml-1">open positions</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="group-hover:scale-105 transition-transform duration-200 bg-transparent"
                    >
                      View Jobs
                      <ExternalLink className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center"
        >
          <Button size="lg" variant="outline" className="text-lg px-8 bg-transparent">
            Explore All Companies
            <ExternalLink className="w-5 h-5 ml-2" />
          </Button>
        </motion.div>

      </div>
    </section>
  )
}
