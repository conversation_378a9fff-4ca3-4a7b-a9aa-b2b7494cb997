'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useCompaniesStore, useJobsStore, useAuthStore, type Company } from '@/stores'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { JobCard } from '@/components/jobs/job-card'
import { ButtonLoading } from '@/components/ui/button-loading'
import { 
  Building, 
  MapPin, 
  Globe, 
  Users, 
  Calendar,
  Star,
  Heart,
  HeartOff,
  Edit,
  Share2,
  ExternalLink,
  Briefcase,
  Award,
  Target
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface CompanyProfilePageProps {
  company: Company
  isOwner?: boolean
  className?: string
}

export function CompanyProfilePage({ company, isOwner = false, className }: CompanyProfilePageProps) {
  const router = useRouter()
  const { user, isAuthenticated } = useAuthStore()
  const { followCompany, unfollowCompany, followLoading, followedCompanies } = useCompaniesStore()
  const { jobs, getJobsByCompany, jobsLoading } = useJobsStore()
  
  const [activeTab, setActiveTab] = useState('overview')
  const [showAllJobs, setShowAllJobs] = useState(false)

  const isFollowing = followedCompanies.includes(company._id)
  const companyJobs = jobs.filter(job => job.company._id === company._id)
  const displayedJobs = showAllJobs ? companyJobs : companyJobs.slice(0, 6)

  // Handle follow/unfollow
  const handleFollowToggle = async () => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    try {
      if (isFollowing) {
        await unfollowCompany(company._id)
      } else {
        await followCompany(company._id)
      }
    } catch (error) {
      console.error('Failed to toggle follow:', error)
    }
  }

  // Load company jobs
  React.useEffect(() => {
    getJobsByCompany(company._id)
  }, [company._id, getJobsByCompany])

  // Format company size
  const formatCompanySize = (size: string) => {
    return size.replace('employees', 'people')
  }

  // Format founded year
  const formatFounded = (founded: Date) => {
    return `Founded in ${new Date(founded).getFullYear()}`
  }

  return (
    <div className={cn('max-w-6xl mx-auto space-y-8', className)}>
      {/* Cover Image */}
      {company.coverImage && (
        <div className="relative h-64 md:h-80 rounded-lg overflow-hidden">
          <img
            src={company.coverImage}
            alt={`${company.name} cover`}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
        </div>
      )}

      {/* Company Header */}
      <Card className={cn(!company.coverImage && 'mt-8')}>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-6">
              {/* Company Logo */}
              <div className="flex-shrink-0">
                {company.logo ? (
                  <img
                    src={company.logo}
                    alt={`${company.name} logo`}
                    className="w-20 h-20 md:w-24 md:h-24 rounded-lg object-cover border-2 border-background shadow-lg"
                  />
                ) : (
                  <div className="w-20 h-20 md:w-24 md:h-24 rounded-lg bg-muted flex items-center justify-center border-2 border-background shadow-lg">
                    <Building className="w-10 h-10 md:w-12 md:h-12 text-muted-foreground" />
                  </div>
                )}
              </div>

              {/* Company Info */}
              <div className="flex-1 min-w-0">
                <h1 className="text-3xl md:text-4xl font-bold mb-2">{company.name}</h1>
                
                <div className="flex flex-wrap items-center gap-4 text-muted-foreground mb-4">
                  <div className="flex items-center space-x-1">
                    <Building className="w-4 h-4" />
                    <span>{company.industry}</span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-4 h-4" />
                    <span>{company.location.city}, {company.location.state}</span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <Users className="w-4 h-4" />
                    <span>{formatCompanySize(company.size)}</span>
                  </div>
                  
                  {company.founded && (
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>{formatFounded(company.founded)}</span>
                    </div>
                  )}
                  
                  {company.website && (
                    <a
                      href={company.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-1 hover:text-primary transition-colors"
                    >
                      <Globe className="w-4 h-4" />
                      <span>Website</span>
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  )}
                </div>

                <p className="text-muted-foreground text-lg leading-relaxed">
                  {company.description}
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2 ml-4">
              <Button variant="outline" size="sm">
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
              
              {isOwner ? (
                <Button
                  onClick={() => router.push(`/company/${company._id}/edit`)}
                  size="sm"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Profile
                </Button>
              ) : (
                <ButtonLoading
                  variant={isFollowing ? "outline" : "default"}
                  onClick={handleFollowToggle}
                  loading={followLoading}
                  size="sm"
                >
                  {isFollowing ? (
                    <>
                      <HeartOff className="w-4 h-4 mr-2" />
                      Unfollow
                    </>
                  ) : (
                    <>
                      <Heart className="w-4 h-4 mr-2" />
                      Follow
                    </>
                  )}
                </ButtonLoading>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Company Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{companyJobs.length}</div>
            <div className="text-sm text-muted-foreground">Open Positions</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{company.followersCount || 0}</div>
            <div className="text-sm text-muted-foreground">Followers</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">4.5</div>
            <div className="text-sm text-muted-foreground">Rating</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">
              {company.founded ? new Date().getFullYear() - new Date(company.founded).getFullYear() : 'N/A'}
            </div>
            <div className="text-sm text-muted-foreground">Years</div>
          </CardContent>
        </Card>
      </div>

      {/* Company Details Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="jobs">
            Jobs ({companyJobs.length})
          </TabsTrigger>
          <TabsTrigger value="culture">Culture</TabsTrigger>
          <TabsTrigger value="benefits">Benefits</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Mission */}
          {company.mission && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="w-5 h-5" />
                  <span>Our Mission</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">{company.mission}</p>
              </CardContent>
            </Card>
          )}

          {/* Specialties */}
          {company.specialties && company.specialties.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Award className="w-5 h-5" />
                  <span>Specialties</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {company.specialties.map((specialty, index) => (
                    <Badge key={index} variant="secondary">
                      {specialty}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recent Jobs */}
          {companyJobs.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Briefcase className="w-5 h-5" />
                    <span>Recent Job Openings</span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setActiveTab('jobs')}
                  >
                    View All Jobs
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {companyJobs.slice(0, 4).map((job) => (
                    <JobCard
                      key={job._id}
                      job={job}
                      variant="compact"
                      showCompanyLogo={false}
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Jobs Tab */}
        <TabsContent value="jobs" className="space-y-6">
          {jobsLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-muted-foreground mt-2">Loading jobs...</p>
            </div>
          ) : companyJobs.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Briefcase className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Open Positions</h3>
                <p className="text-muted-foreground">
                  {company.name} doesn't have any open positions at the moment.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                {displayedJobs.map((job) => (
                  <JobCard
                    key={job._id}
                    job={job}
                    showCompanyLogo={false}
                  />
                ))}
              </div>
              
              {companyJobs.length > 6 && !showAllJobs && (
                <div className="text-center">
                  <Button
                    variant="outline"
                    onClick={() => setShowAllJobs(true)}
                  >
                    Show All {companyJobs.length} Jobs
                  </Button>
                </div>
              )}
            </div>
          )}
        </TabsContent>

        {/* Culture Tab */}
        <TabsContent value="culture" className="space-y-6">
          {company.culture ? (
            <Card>
              <CardHeader>
                <CardTitle>Company Culture</CardTitle>
                <CardDescription>
                  Learn about our values, work environment, and what makes us unique
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed whitespace-pre-wrap">
                  {company.culture}
                </p>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Building className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Culture Information Coming Soon</h3>
                <p className="text-muted-foreground">
                  {company.name} hasn't shared their culture information yet.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Benefits Tab */}
        <TabsContent value="benefits" className="space-y-6">
          {company.benefits && company.benefits.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Benefits & Perks</CardTitle>
                <CardDescription>
                  What we offer our team members
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {company.benefits.map((benefit, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-muted/50 rounded-lg">
                      <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">{benefit}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Award className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Benefits Information Coming Soon</h3>
                <p className="text-muted-foreground">
                  {company.name} hasn't listed their benefits yet.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
