"use client"

import React from "react"
import { usePathname, useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { 
  LayoutDashboard,
  Briefcase,
  Users,
  BarChart3,
  Building2,
  Settings,
  CreditCard,
  HelpCircle,
  Plus,
  Eye,
  Edit,
  Archive,
  UserCheck,
  Clock,
  TrendingUp,
  Target,
  ChevronRight,
  Home
} from "lucide-react"
import { cn } from "@/lib/utils"
import Link from "next/link"

interface NavigationItem {
  title: string
  url: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string | number
  items?: NavigationItem[]
}

const navigationItems: NavigationItem[] = [
  {
    title: "Overview",
    url: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Jobs",
    url: "/dashboard/jobs",
    icon: Briefcase,
    badge: "12",
    items: [
      {
        title: "All Jobs",
        url: "/dashboard/jobs",
        icon: Eye,
      },
      {
        title: "Create Job",
        url: "/dashboard/jobs/create",
        icon: Plus,
      },
      {
        title: "Draft Jobs",
        url: "/dashboard/jobs/drafts",
        icon: Edit,
        badge: "3",
      },
      {
        title: "Archived Jobs",
        url: "/dashboard/jobs/archived",
        icon: Archive,
      },
    ],
  },
  {
    title: "Applications",
    url: "/dashboard/applications",
    icon: Users,
    badge: "47",
    items: [
      {
        title: "All Applications",
        url: "/dashboard/applications",
        icon: Users,
      },
      {
        title: "Pending Review",
        url: "/dashboard/applications/pending",
        icon: Clock,
        badge: "23",
      },
      {
        title: "Shortlisted",
        url: "/dashboard/applications/shortlisted",
        icon: UserCheck,
        badge: "8",
      },
      {
        title: "Interviews",
        url: "/dashboard/applications/interviews",
        icon: Target,
        badge: "5",
      },
    ],
  },
  {
    title: "Analytics",
    url: "/dashboard/analytics",
    icon: BarChart3,
    items: [
      {
        title: "Overview",
        url: "/dashboard/analytics",
        icon: TrendingUp,
      },
      {
        title: "Job Performance",
        url: "/dashboard/analytics/jobs",
        icon: Briefcase,
      },
      {
        title: "Hiring Metrics",
        url: "/dashboard/analytics/hiring",
        icon: Target,
      },
    ],
  },
]

const companyItems: NavigationItem[] = [
  {
    title: "Company Profile",
    url: "/dashboard/company",
    icon: Building2,
  },
  {
    title: "Billing & Plans",
    url: "/dashboard/billing",
    icon: CreditCard,
  },
  {
    title: "Settings",
    url: "/dashboard/settings",
    icon: Settings,
  },
]

const supportItems: NavigationItem[] = [
  {
    title: "Help & Support",
    url: "/help",
    icon: HelpCircle,
  },
  {
    title: "Back to Site",
    url: "/",
    icon: Home,
  },
]

export function DashboardSidebar() {
  const pathname = usePathname()
  const router = useRouter()
  const { state } = useSidebar()

  const isCollapsed = state === "collapsed"

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const isActive = pathname === item.url || pathname.startsWith(item.url + "/")
    const hasSubItems = item.items && item.items.length > 0

    if (hasSubItems) {
      return (
        <Collapsible key={item.title} defaultOpen={isActive}>
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton
                className={cn(
                  "w-full justify-between",
                  isActive && "bg-primary/10 text-primary font-medium"
                )}
              >
                <div className="flex items-center space-x-3">
                  <item.icon className="w-5 h-5" />
                  {!isCollapsed && (
                    <>
                      <span>{item.title}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="ml-auto">
                          {item.badge}
                        </Badge>
                      )}
                    </>
                  )}
                </div>
                {!isCollapsed && <ChevronRight className="w-4 h-4" />}
              </SidebarMenuButton>
            </CollapsibleTrigger>
            {!isCollapsed && (
              <CollapsibleContent>
                <SidebarMenuSub>
                  {item.items?.map((subItem) => (
                    <SidebarMenuSubItem key={subItem.title}>
                      <SidebarMenuSubButton
                        asChild
                        className={cn(
                          pathname === subItem.url && "bg-primary/10 text-primary font-medium"
                        )}
                      >
                        <Link href={subItem.url} className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <subItem.icon className="w-4 h-4" />
                            <span>{subItem.title}</span>
                          </div>
                          {subItem.badge && (
                            <Badge variant="secondary" className="text-xs">
                              {subItem.badge}
                            </Badge>
                          )}
                        </Link>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  ))}
                </SidebarMenuSub>
              </CollapsibleContent>
            )}
          </SidebarMenuItem>
        </Collapsible>
      )
    }

    return (
      <SidebarMenuItem key={item.title}>
        <SidebarMenuButton
          asChild
          className={cn(
            isActive && "bg-primary/10 text-primary font-medium"
          )}
        >
          <Link href={item.url} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <item.icon className="w-5 h-5" />
              {!isCollapsed && <span>{item.title}</span>}
            </div>
            {!isCollapsed && item.badge && (
              <Badge variant="secondary">
                {item.badge}
              </Badge>
            )}
          </Link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    )
  }

  return (
    <Sidebar variant="inset" className="border-r">
      <SidebarHeader className="border-b px-6 py-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <Building2 className="w-5 h-5 text-primary-foreground" />
          </div>
          {!isCollapsed && (
            <div>
              <h2 className="text-lg font-semibold">JobPortal</h2>
              <p className="text-xs text-muted-foreground">Company Dashboard</p>
            </div>
          )}
        </div>
      </SidebarHeader>

      <SidebarContent>
        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarGroupLabel>Main</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator />

        {/* Company Management */}
        <SidebarGroup>
          <SidebarGroupLabel>Company</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {companyItems.map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator />

        {/* Support & Help */}
        <SidebarGroup>
          <SidebarGroupLabel>Support</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {supportItems.map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t p-4">
        {!isCollapsed && (
          <div className="space-y-2">
            <div className="text-xs text-muted-foreground">
              Current Plan: Professional
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div className="bg-primary h-2 rounded-full" style={{ width: "75%" }} />
            </div>
            <div className="text-xs text-muted-foreground">
              15 of 20 job postings used
            </div>
            <Button size="sm" variant="outline" className="w-full">
              Upgrade Plan
            </Button>
          </div>
        )}
      </SidebarFooter>
    </Sidebar>
  )
}
