'use client'

import { useState, useEffect } from 'react'
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"

export function HeroSectionPlaceholder() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  // Use placeholder.com images for testing
  const backgroundImages = [
    { 
      url: "https://picsum.photos/1920/1080?random=1", 
      alt: "Placeholder image 1"
    },
    { 
      url: "https://picsum.photos/1920/1080?random=2", 
      alt: "Placeholder image 2"
    },
    { 
      url: "https://picsum.photos/1920/1080?random=3", 
      alt: "Placeholder image 3"
    },
    { 
      url: "https://picsum.photos/1920/1080?random=4", 
      alt: "Placeholder image 4"
    },
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % backgroundImages.length)
    }, 3000)
    return () => clearInterval(interval)
  }, [backgroundImages.length])

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Debug info */}
      <div className="absolute top-4 left-4 z-50 bg-black/80 text-white p-4 rounded text-sm">
        <div>Current image: {currentImageIndex + 1}</div>
        <div>Using: Picsum placeholder images</div>
      </div>

      {/* Background images */}
      <div className="absolute inset-0 -z-20">
        {/* Fallback background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-900" />
        
        {/* Placeholder images */}
        {backgroundImages.map((image, index) => (
          <motion.img
            key={index}
            src={image.url}
            alt={image.alt}
            initial={{ opacity: 0 }}
            animate={{
              opacity: index === currentImageIndex ? 1 : 0,
              scale: index === currentImageIndex ? 1 : 1.05
            }}
            transition={{
              duration: 1.5,
              ease: "easeInOut",
              opacity: { duration: 1.2 }
            }}
            className="absolute inset-0 w-full h-full object-cover"
            onLoad={() => console.log(`✅ Placeholder image loaded: ${index + 1}`)}
            onError={(e) => console.error(`❌ Placeholder image failed: ${index + 1}`, e)}
          />
        ))}
      </div>

      {/* Dark overlay */}
      <div className="absolute inset-0 bg-black/40 -z-10" />

      {/* Content */}
      <div className="relative z-10 text-center text-white px-4 max-w-4xl mx-auto">
        <motion.h1 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-4xl md:text-6xl font-bold mb-6"
        >
          Find Your Dream Job
        </motion.h1>
        
        <motion.p 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-xl md:text-2xl mb-8 max-w-2xl mx-auto"
        >
          Connect with top employers and discover opportunities that match your skills and aspirations.
        </motion.p>
        
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="flex flex-col sm:flex-row gap-4 justify-center"
        >
          <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
            Find Jobs
          </Button>
          <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3">
            Post a Job
          </Button>
        </motion.div>

        {/* Image indicators */}
        <div className="flex justify-center space-x-2 mt-8">
          {backgroundImages.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentImageIndex(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentImageIndex
                  ? 'bg-white scale-125'
                  : 'bg-white/50 hover:bg-white/75'
              }`}
              aria-label={`Switch to image ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </section>
  )
}
