// "use client"

// import { motion } from "framer-motion"
// import { <PERSON><PERSON> } from "@/components/ui/button"
// import { Badge } from "@/components/ui/badge"
// import { Card, CardContent, CardHeader } from "@/components/ui/card"
// import { MapP<PERSON>, Clock, DollarSign, Bookmark, Star } from "lucide-react"
// import Image from "next/image"

// interface Job {
//   id: number
//   title: string
//   company: string
//   location: string
//   type: string
//   salary: string
//   tags: string[]
//   logo: string
//   posted: string
//   featured: boolean
// }

// interface JobCardProps {
//   job: Job
//   index: number
// }

// export function JobCard({ job, index }: JobCardProps) {
//   const itemVariants = {
//     hidden: { opacity: 0, y: 30 },
//     visible: {
//       opacity: 1,
//       y: 0,
//       transition: {
//         duration: 0.6,
//         ease: "easeOut",
//       },
//     },
//   }

//   return (
//     <motion.div variants={itemVariants}>
//       <Card className="group hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20 bg-background/80 backdrop-blur-sm relative overflow-hidden">
//         {job.featured && (
//           <div className="absolute top-4 right-4 z-10">
//             <Badge className="bg-primary/10 text-primary border-primary/20">
//               <Star className="w-3 h-3 mr-1 fill-current" />
//               Featured
//             </Badge>
//           </div>
//         )}

//         <CardHeader className="pb-4">
//           <div className="flex items-start justify-between">
//             <div className="flex items-center space-x-4">
//               <motion.div
//                 whileHover={{ scale: 1.1 }}
//                 className="w-12 h-12 rounded-xl bg-muted flex items-center justify-center overflow-hidden"
//               >
//                 <Image
//                   src={job.logo || "/placeholder.svg"}
//                   alt={`${job.company} logo`}
//                   width={48}
//                   height={48}
//                   className="w-full h-full object-cover"
//                 />
//               </motion.div>
//               <div>
//                 <h3 className="font-semibold text-lg group-hover:text-primary transition-colors duration-200">
//                   {job.title}
//                 </h3>
//                 <p className="text-muted-foreground">{job.company}</p>
//               </div>
//             </div>
//             <Button
//               variant="ghost"
//               size="icon"
//               className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
//             >
//               <Bookmark className="w-4 h-4" />
//             </Button>
//           </div>
//         </CardHeader>

//         <CardContent className="space-y-4">
//           <div className="flex items-center space-x-4 text-sm text-muted-foreground">
//             <div className="flex items-center space-x-1">
//               <MapPin className="w-4 h-4" />
//               <span>{job.location}</span>
//             </div>
//             <div className="flex items-center space-x-1">
//               <Clock className="w-4 h-4" />
//               <span>{job.type}</span>
//             </div>
//           </div>

//           <div className="flex items-center space-x-1 text-sm font-medium text-primary">
//             <DollarSign className="w-4 h-4" />
//             <span>{job.salary}</span>
//           </div>

//           <div className="flex flex-wrap gap-2">
//             {job.tags.map((tag) => (
//               <Badge key={tag} variant="secondary" className="text-xs">
//                 {tag}
//               </Badge>
//             ))}
//           </div>

//           <div className="flex items-center justify-between pt-4 border-t border-border/50">
//             <span className="text-sm text-muted-foreground">{job.posted}</span>
//             <Button size="sm" className="group-hover:scale-105 transition-transform duration-200">
//               Apply Now
//             </Button>
//           </div>
//         </CardContent>
//       </Card>
//     </motion.div>
//   )
// }



"use client"

import { motion, Variants } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { MapPin, Clock, DollarSign, Bookmark, Star } from "lucide-react"
import Image from "next/image"

interface Job {
  id: number
  title: string
  company: string
  location: string
  type: string
  salary: string
  tags: string[]
  logo: string
  posted: string
  featured: boolean
}

interface JobCardProps {
  job: Job
  index: number
}

export function JobCard({ job, index }: JobCardProps) {
  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut" as const,
      },
    },
  }

  return (
    <motion.div variants={itemVariants}>
      <Card className="group hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20 bg-background/80 backdrop-blur-sm relative overflow-hidden">
        {job.featured && (
          <div className="absolute top-4 right-4 z-10">
            <Badge className="bg-primary/10 text-primary border-primary/20">
              <Star className="w-3 h-3 mr-1 fill-current" />
              Featured
            </Badge>
          </div>
        )}

        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <motion.div whileHover={{ scale: 1.1 }} className="w-12 h-12 rounded-xl bg-muted flex items-center justify-center overflow-hidden">
                <Image
                  src={job.logo || "/placeholder.svg"}
                  alt={`${job.company} logo`}
                  width={48}
                  height={48}
                  className="w-full h-full object-cover"
                />
              </motion.div>
              <div>
                <h3 className="font-semibold text-lg group-hover:text-primary transition-colors duration-200">
                  {job.title}
                </h3>
                <p className="text-muted-foreground">{job.company}</p>
              </div>
            </div>
            <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <Bookmark className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-1">
              <MapPin className="w-4 h-4" />
              <span>{job.location}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>{job.type}</span>
            </div>
          </div>

          <div className="flex items-center space-x-1 text-sm font-medium text-primary">
            <DollarSign className="w-4 h-4" />
            <span>{job.salary}</span>
          </div>

          <div className="flex flex-wrap gap-2">
            {job.tags.map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>

          <div className="flex items-center justify-between pt-4 border-t border-border/50">
            <span className="text-sm text-muted-foreground">{job.posted}</span>
            <Button size="sm" className="group-hover:scale-105 transition-transform duration-200">
              Apply Now
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
