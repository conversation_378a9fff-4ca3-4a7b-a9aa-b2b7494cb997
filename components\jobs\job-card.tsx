'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { useJobsStore, useAuthStore, type Job } from '@/stores'
import { getApplicationStatusInfo } from '@/lib/job-data'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ButtonLoading } from '@/components/ui/button-loading'
import { 
  MapPin, 
  Clock, 
  DollarSign, 
  Building, 
  Bookmark, 
  BookmarkCheck,
  ExternalLink,
  Calendar
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface JobCardProps {
  job: Job
  variant?: 'default' | 'compact' | 'featured'
  showCompanyLogo?: boolean
  className?: string
}

export function JobCard({ 
  job, 
  variant = 'default',
  showCompanyLogo = true,
  className 
}: JobCardProps) {
  const router = useRouter()
  const { isAuthenticated } = useAuthStore()
  const { 
    savedJobs, 
    saveJob, 
    unsaveJob, 
    saveLoading 
  } = useJobsStore()

  const isSaved = savedJobs.includes(job._id)
  const applicationStatusInfo = getApplicationStatusInfo(job)

  // Format salary display
  const formatSalary = () => {
    if (!job.salary.min && !job.salary.max) return null
    
    const formatAmount = (amount: number) => {
      if (amount >= 1000000) return `${(amount / 1000000).toFixed(1)}M`
      if (amount >= 1000) return `${(amount / 1000).toFixed(0)}K`
      return amount.toString()
    }

    const period = job.salary.period === 'yearly' ? '/year' : 
                   job.salary.period === 'monthly' ? '/month' : '/hour'

    if (job.salary.min && job.salary.max) {
      return `$${formatAmount(job.salary.min)} - $${formatAmount(job.salary.max)}${period}`
    } else if (job.salary.min) {
      return `$${formatAmount(job.salary.min)}+${period}`
    } else if (job.salary.max) {
      return `Up to $${formatAmount(job.salary.max)}${period}`
    }
    return null
  }

  // Format posted date
  const formatPostedDate = () => {
    const now = new Date()
    const posted = new Date(job.postedAt)
    const diffInDays = Math.floor((now.getTime() - posted.getTime()) / (1000 * 60 * 60 * 24))
    
    if (diffInDays === 0) return 'Today'
    if (diffInDays === 1) return 'Yesterday'
    if (diffInDays < 7) return `${diffInDays} days ago`
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`
    return posted.toLocaleDateString()
  }

  // Handle save/unsave job
  const handleSaveToggle = async (e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    try {
      if (isSaved) {
        await unsaveJob(job._id)
      } else {
        await saveJob(job._id)
      }
    } catch (error) {
      console.error('Failed to toggle job save:', error)
    }
  }

  // Handle card click
  const handleCardClick = () => {
    router.push(`/jobs/${job._id}`)
  }

  // Handle apply click
  const handleApplyClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (!isAuthenticated) {
      router.push('/login')
      return
    }
    
    router.push(`/jobs/${job._id}/apply`)
  }

  const cardVariants = {
    default: 'p-6',
    compact: 'p-4',
    featured: 'p-6 border-primary/20 bg-primary/5'
  }

  return (
    <Card 
      className={cn(
        'cursor-pointer transition-all duration-200 hover:shadow-lg hover:border-primary/20',
        cardVariants[variant],
        className
      )}
      onClick={handleCardClick}
    >
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4 flex-1">
            {/* Company Logo */}
            {showCompanyLogo && (
              <div className="flex-shrink-0">
                {job.company.logo ? (
                  <img
                    src={job.company.logo}
                    alt={`${job.company.name} logo`}
                    className="w-12 h-12 rounded-lg object-cover border"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-lg bg-muted flex items-center justify-center border">
                    <Building className="w-6 h-6 text-muted-foreground" />
                  </div>
                )}
              </div>
            )}

            {/* Job Info */}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg leading-tight mb-1 truncate">
                {job.title}
              </h3>
              <div className="flex items-center space-x-2 text-muted-foreground mb-2">
                <span className="font-medium">{job.company.name}</span>
                <span>•</span>
                <div className="flex items-center space-x-1">
                  <MapPin className="w-3 h-3" />
                  <span className="text-sm">
                    {job.location.remote ? 'Remote' : 
                     `${job.location.city}, ${job.location.state}`}
                  </span>
                </div>
              </div>
              
              {/* Job Type & Posted Date */}
              <div className="flex items-center space-x-2 mb-3">
                <Badge variant="secondary" className="text-xs">
                  {job.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </Badge>
                <Badge
                  variant="secondary"
                  className={`text-xs ${
                    applicationStatusInfo.color === 'green' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                    applicationStatusInfo.color === 'orange' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                    applicationStatusInfo.color === 'red' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                    'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                  }`}
                >
                  {applicationStatusInfo.label}
                </Badge>
                <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                  <Clock className="w-3 h-3" />
                  <span>{formatPostedDate()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <ButtonLoading
            variant="ghost"
            size="sm"
            loading={saveLoading}
            onClick={handleSaveToggle}
            className="flex-shrink-0 h-8 w-8 p-0"
          >
            {isSaved ? (
              <BookmarkCheck className="w-4 h-4 text-primary" />
            ) : (
              <Bookmark className="w-4 h-4" />
            )}
          </ButtonLoading>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Job Description */}
        <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
          {job.description}
        </p>

        {/* Salary */}
        {formatSalary() && (
          <div className="flex items-center space-x-1 mb-4">
            <DollarSign className="w-4 h-4 text-green-600" />
            <span className="font-medium text-green-600">
              {formatSalary()}
            </span>
          </div>
        )}

        {/* Skills/Requirements */}
        {job.skills && job.skills.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {job.skills.slice(0, 4).map((skill, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="text-xs"
                >
                  {skill}
                </Badge>
              ))}
              {job.skills.length > 4 && (
                <Badge variant="outline" className="text-xs">
                  +{job.skills.length - 4} more
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
            <span>{job.applicationsCount} applicants</span>
            {job.applicationDeadline && (
              <div className="flex items-center space-x-1">
                <Calendar className="w-3 h-3" />
                <span>
                  Deadline: {new Date(job.applicationDeadline).toLocaleDateString()}
                </span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                handleCardClick()
              }}
            >
              <ExternalLink className="w-3 h-3 mr-1" />
              View
            </Button>
            <Button
              size="sm"
              onClick={handleApplyClick}
              className="bg-primary hover:bg-primary/90"
            >
              Apply Now
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Compact version for lists
export function JobCardCompact({ job, className }: { job: Job; className?: string }) {
  return (
    <JobCard 
      job={job} 
      variant="compact" 
      showCompanyLogo={false}
      className={className}
    />
  )
}

// Featured version for promoted jobs
export function JobCardFeatured({ job, className }: { job: Job; className?: string }) {
  return (
    <JobCard 
      job={job} 
      variant="featured" 
      className={className}
    />
  )
}
