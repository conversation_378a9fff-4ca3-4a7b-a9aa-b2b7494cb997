'use client'

import React, { useState } from 'react'
import { useJobsStore, type JobFilters } from '@/stores'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { 
  MapPin, 
  DollarSign, 
  Briefcase, 
  Clock, 
  Building, 
  Filter,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface JobSearchFiltersProps {
  className?: string
  onFiltersChange?: (filters: Partial<JobFilters>) => void
  showApplyButton?: boolean
}

export function JobSearchFilters({ 
  className, 
  onFiltersChange,
  showApplyButton = true 
}: JobSearchFiltersProps) {
  const { filters, updateFilters } = useJobsStore()
  
  const [localFilters, setLocalFilters] = useState<Partial<JobFilters>>(filters)
  const [expandedSections, setExpandedSections] = useState({
    jobType: true,
    salary: true,
    location: true,
    experience: true,
    company: false,
    datePosted: true
  })

  // Job type options
  const jobTypes = [
    { value: 'full-time', label: 'Full-time' },
    { value: 'part-time', label: 'Part-time' },
    { value: 'contract', label: 'Contract' },
    { value: 'internship', label: 'Internship' }
  ]

  // Experience level options
  const experienceLevels = [
    { value: 'entry', label: 'Entry Level' },
    { value: 'mid', label: 'Mid Level' },
    { value: 'senior', label: 'Senior Level' },
    { value: 'lead', label: 'Lead/Principal' },
    { value: 'executive', label: 'Executive' }
  ]

  // Date posted options
  const datePostedOptions = [
    { value: 'any', label: 'Any time' },
    { value: '24h', label: 'Last 24 hours' },
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' }
  ]

  // Popular locations
  const popularLocations = [
    'New York, NY',
    'San Francisco, CA',
    'Los Angeles, CA',
    'Chicago, IL',
    'Boston, MA',
    'Seattle, WA',
    'Austin, TX',
    'Denver, CO'
  ]

  // Handle filter changes
  const handleFilterChange = (key: keyof JobFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value }
    setLocalFilters(newFilters)
    
    if (onFiltersChange) {
      onFiltersChange(newFilters)
    }
  }

  // Handle job type selection
  const handleJobTypeChange = (jobType: string, checked: boolean) => {
    const currentTypes = localFilters.jobTypes || []
    const newTypes = checked 
      ? [...currentTypes, jobType]
      : currentTypes.filter(type => type !== jobType)
    
    handleFilterChange('jobTypes', newTypes)
  }

  // Handle experience level selection
  const handleExperienceChange = (level: string, checked: boolean) => {
    const currentLevels = localFilters.experienceLevels || []
    const newLevels = checked 
      ? [...currentLevels, level]
      : currentLevels.filter(l => l !== level)
    
    handleFilterChange('experienceLevels', newLevels)
  }

  // Handle salary range change
  const handleSalaryChange = (values: number[]) => {
    handleFilterChange('salaryRange', [values[0], values[1]] as [number, number])
  }

  // Apply filters
  const applyFilters = () => {
    updateFilters(localFilters)
  }

  // Clear all filters
  const clearFilters = () => {
    const emptyFilters: Partial<JobFilters> = {
      jobTypes: [],
      experienceLevels: [],
      salaryRange: [0, 200000],
      locations: [],
      companies: [],
      remote: false,
      datePosted: 'any'
    }
    setLocalFilters(emptyFilters)
    updateFilters(emptyFilters)
    
    if (onFiltersChange) {
      onFiltersChange(emptyFilters)
    }
  }

  // Toggle section expansion
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0
    if (localFilters.jobTypes?.length) count += localFilters.jobTypes.length
    if (localFilters.experienceLevels?.length) count += localFilters.experienceLevels.length
    if (localFilters.locations?.length) count += localFilters.locations.length
    if (localFilters.companies?.length) count += localFilters.companies.length
    if (localFilters.remote) count += 1
    if (localFilters.datePosted && localFilters.datePosted !== 'any') count += 1
    return count
  }

  const FilterSection = ({ 
    title, 
    icon: Icon, 
    sectionKey, 
    children 
  }: { 
    title: string
    icon: any
    sectionKey: keyof typeof expandedSections
    children: React.ReactNode 
  }) => (
    <Collapsible 
      open={expandedSections[sectionKey]} 
      onOpenChange={() => toggleSection(sectionKey)}
    >
      <CollapsibleTrigger className="flex items-center justify-between w-full p-3 hover:bg-muted/50 rounded-lg transition-colors">
        <div className="flex items-center space-x-2">
          <Icon className="w-4 h-4" />
          <span className="font-medium">{title}</span>
        </div>
        {expandedSections[sectionKey] ? (
          <ChevronUp className="w-4 h-4" />
        ) : (
          <ChevronDown className="w-4 h-4" />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent className="px-3 pb-3">
        {children}
      </CollapsibleContent>
    </Collapsible>
  )

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Filter className="w-5 h-5" />
            <span>Filters</span>
            {getActiveFilterCount() > 0 && (
              <Badge variant="secondary" className="ml-2">
                {getActiveFilterCount()}
              </Badge>
            )}
          </CardTitle>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={clearFilters}
            className="text-muted-foreground hover:text-foreground"
          >
            Clear all
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-1">
        {/* Job Type */}
        <FilterSection title="Job Type" icon={Briefcase} sectionKey="jobType">
          <div className="space-y-3">
            {jobTypes.map((type) => (
              <div key={type.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`jobtype-${type.value}`}
                  checked={localFilters.jobTypes?.includes(type.value) || false}
                  onCheckedChange={(checked) => 
                    handleJobTypeChange(type.value, !!checked)
                  }
                />
                <Label 
                  htmlFor={`jobtype-${type.value}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {type.label}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Salary Range */}
        <FilterSection title="Salary Range" icon={DollarSign} sectionKey="salary">
          <div className="space-y-4">
            <div className="px-2">
              <Slider
                value={localFilters.salaryRange || [0, 200000]}
                onValueChange={handleSalaryChange}
                max={200000}
                min={0}
                step={5000}
                className="w-full"
              />
            </div>
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>${(localFilters.salaryRange?.[0] || 0).toLocaleString()}</span>
              <span>${(localFilters.salaryRange?.[1] || 200000).toLocaleString()}+</span>
            </div>
          </div>
        </FilterSection>

        {/* Location */}
        <FilterSection title="Location" icon={MapPin} sectionKey="location">
          <div className="space-y-3">
            {/* Remote Option */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="remote"
                checked={localFilters.remote || false}
                onCheckedChange={(checked) => 
                  handleFilterChange('remote', !!checked)
                }
              />
              <Label htmlFor="remote" className="text-sm font-normal cursor-pointer">
                Remote
              </Label>
            </div>
            
            {/* Popular Locations */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">
                Popular Locations
              </Label>
              <div className="flex flex-wrap gap-1">
                {popularLocations.slice(0, 4).map((location) => (
                  <Badge
                    key={location}
                    variant="outline"
                    className="text-xs cursor-pointer hover:bg-primary hover:text-primary-foreground"
                    onClick={() => {
                      const currentLocations = localFilters.locations || []
                      if (!currentLocations.includes(location)) {
                        handleFilterChange('locations', [...currentLocations, location])
                      }
                    }}
                  >
                    {location}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </FilterSection>

        {/* Experience Level */}
        <FilterSection title="Experience Level" icon={Clock} sectionKey="experience">
          <div className="space-y-3">
            {experienceLevels.map((level) => (
              <div key={level.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`experience-${level.value}`}
                  checked={localFilters.experienceLevels?.includes(level.value) || false}
                  onCheckedChange={(checked) => 
                    handleExperienceChange(level.value, !!checked)
                  }
                />
                <Label 
                  htmlFor={`experience-${level.value}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {level.label}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Date Posted */}
        <FilterSection title="Date Posted" icon={Clock} sectionKey="datePosted">
          <Select
            value={localFilters.datePosted || 'any'}
            onValueChange={(value) => 
              handleFilterChange('datePosted', value as JobFilters['datePosted'])
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {datePostedOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </FilterSection>

        {/* Apply Button */}
        {showApplyButton && (
          <div className="pt-4 border-t">
            <Button 
              onClick={applyFilters} 
              className="w-full"
              size="lg"
            >
              Apply Filters
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
