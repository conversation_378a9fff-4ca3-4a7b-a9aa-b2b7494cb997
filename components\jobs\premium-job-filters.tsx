'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Filter, 
  X, 
  MapPin, 
  Building, 
  Clock, 
  DollarSign,
  Briefcase,
  GraduationCap,
  ChevronDown,
  ChevronUp,
  Code,
  Palette,
  Database,
  TrendingUp,
  Users,
  Heart,
  Scale,
  Zap,
  Globe,
  Target,
  Lightbulb,
  Shield,
  Home,
  Car,
  Plane,
  Calculator,
  Stethoscope,
  Megaphone
} from 'lucide-react'

interface JobFiltersProps {
  selectedCategories: string[]
  setSelectedCategories: (categories: string[]) => void
  selectedJobTypes: string[]
  setSelectedJobTypes: (types: string[]) => void
  selectedExperience: string[]
  setSelectedExperience: (experience: string[]) => void
  salaryRange: number[]
  setSalaryRange: (range: number[]) => void
  selectedWorkModel: string[]
  setSelectedWorkModel: (models: string[]) => void
  onFiltersChange?: () => void
  showApplyButton?: boolean
}

export function PremiumJobFilters({
  selectedCategories,
  setSelectedCategories,
  selectedJobTypes,
  setSelectedJobTypes,
  selectedExperience,
  setSelectedExperience,
  salaryRange,
  setSalaryRange,
  selectedWorkModel,
  setSelectedWorkModel,
  onFiltersChange,
  showApplyButton = true
}: JobFiltersProps) {
  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    jobType: true,
    experience: true,
    salary: true,
    workModel: true,
    location: true,
    benefits: true
  })

  const [selectedLocation, setSelectedLocation] = useState('')
  const [selectedBenefits, setSelectedBenefits] = useState<string[]>([])

  const jobCategories = [
    {
      name: 'Software Development',
      icon: Code,
      categories: ['Frontend Development', 'Backend Development', 'Full Stack Development', 'Mobile Development', 'DevOps Engineering', 'Software Architecture']
    },
    {
      name: 'Data & Analytics',
      icon: Database,
      categories: ['Data Science', 'Data Engineering', 'Machine Learning', 'Business Intelligence', 'Data Analysis', 'AI Research']
    },
    {
      name: 'Design & Creative',
      icon: Palette,
      categories: ['UX Design', 'UI Design', 'Product Design', 'Graphic Design', 'Brand Design', 'Motion Graphics']
    },
    {
      name: 'Product & Strategy',
      icon: Target,
      categories: ['Product Management', 'Product Marketing', 'Strategy', 'Business Analysis', 'Growth', 'Innovation']
    },
    {
      name: 'Marketing & Sales',
      icon: Megaphone,
      categories: ['Digital Marketing', 'Content Marketing', 'Sales', 'Marketing Operations', 'SEO/SEM', 'Social Media']
    },
    {
      name: 'Finance & Accounting',
      icon: Calculator,
      categories: ['Financial Analysis', 'Accounting', 'Investment Banking', 'Corporate Finance', 'Risk Management', 'Audit']
    },
    {
      name: 'Human Resources',
      icon: Users,
      categories: ['Talent Acquisition', 'HR Business Partner', 'Compensation', 'Learning & Development', 'HR Operations', 'People Analytics']
    },
    {
      name: 'Healthcare & Medical',
      icon: Stethoscope,
      categories: ['Clinical Research', 'Medical Affairs', 'Regulatory Affairs', 'Healthcare IT', 'Nursing', 'Medical Writing']
    },
    {
      name: 'Legal & Compliance',
      icon: Scale,
      categories: ['Corporate Law', 'Compliance', 'Legal Operations', 'Intellectual Property', 'Employment Law', 'Regulatory']
    },
    {
      name: 'Operations & Management',
      icon: Building,
      categories: ['Operations Management', 'Project Management', 'Supply Chain', 'Quality Assurance', 'Process Improvement', 'Consulting']
    }
  ]

  const jobTypes = ['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship']
  const experienceLevels = ['Entry Level', 'Mid Level', 'Senior Level', 'Executive']
  const workModels = ['On-site', 'Remote', 'Hybrid']
  
  const locations = [
    'San Francisco, CA', 'New York, NY', 'Los Angeles, CA', 'Seattle, WA', 'Austin, TX',
    'Boston, MA', 'Chicago, IL', 'Denver, CO', 'Miami, FL', 'Atlanta, GA',
    'Toronto, ON', 'Vancouver, BC', 'London, UK', 'Berlin, Germany', 'Amsterdam, Netherlands',
    'Singapore', 'Tokyo, Japan', 'Sydney, Australia', 'Remote', 'Anywhere'
  ]

  const benefits = [
    { value: 'health-insurance', label: 'Health Insurance', color: 'bg-green-500' },
    { value: 'dental-vision', label: 'Dental & Vision', color: 'bg-blue-500' },
    { value: 'retirement-401k', label: '401(k) / Retirement', color: 'bg-purple-500' },
    { value: 'flexible-hours', label: 'Flexible Hours', color: 'bg-orange-500' },
    { value: 'remote-work', label: 'Remote Work', color: 'bg-teal-500' },
    { value: 'unlimited-pto', label: 'Unlimited PTO', color: 'bg-pink-500' },
    { value: 'learning-budget', label: 'Learning Budget', color: 'bg-indigo-500' },
    { value: 'stock-options', label: 'Stock Options', color: 'bg-yellow-500' },
    { value: 'gym-wellness', label: 'Gym & Wellness', color: 'bg-red-500' },
    { value: 'visa-sponsorship', label: 'Visa Sponsorship', color: 'bg-cyan-500' }
  ]

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const toggleCategory = (category: string) => {
    setSelectedCategories(
      selectedCategories.includes(category)
        ? selectedCategories.filter(c => c !== category)
        : [...selectedCategories, category]
    )
  }

  const toggleJobType = (type: string) => {
    setSelectedJobTypes(
      selectedJobTypes.includes(type)
        ? selectedJobTypes.filter(t => t !== type)
        : [...selectedJobTypes, type]
    )
  }

  const toggleExperience = (level: string) => {
    setSelectedExperience(
      selectedExperience.includes(level)
        ? selectedExperience.filter(e => e !== level)
        : [...selectedExperience, level]
    )
  }

  const toggleWorkModel = (model: string) => {
    setSelectedWorkModel(
      selectedWorkModel.includes(model)
        ? selectedWorkModel.filter(m => m !== model)
        : [...selectedWorkModel, model]
    )
  }

  const toggleBenefit = (benefit: string) => {
    setSelectedBenefits(
      selectedBenefits.includes(benefit)
        ? selectedBenefits.filter(b => b !== benefit)
        : [...selectedBenefits, benefit]
    )
  }

  const clearAllFilters = () => {
    setSelectedCategories([])
    setSelectedJobTypes([])
    setSelectedExperience([])
    setSalaryRange([0, 300000])
    setSelectedWorkModel([])
    setSelectedLocation('')
    setSelectedBenefits([])
  }

  const activeFilterCount = selectedCategories.length + 
    selectedJobTypes.length + 
    selectedExperience.length + 
    selectedWorkModel.length +
    (salaryRange[0] > 0 || salaryRange[1] < 300000 ? 1 : 0) +
    (selectedLocation ? 1 : 0) +
    selectedBenefits.length

  const FilterSection = ({ 
    title, 
    icon: Icon, 
    section, 
    children 
  }: { 
    title: string
    icon: any
    section: keyof typeof expandedSections
    children: React.ReactNode 
  }) => (
    <div className="space-y-3">
      <Button
        variant="ghost"
        onClick={() => toggleSection(section)}
        className="w-full justify-between p-0 h-auto font-medium text-left hover:bg-transparent"
      >
        <div className="flex items-center space-x-2">
          <Icon className="w-4 h-4 text-primary" />
          <span>{title}</span>
        </div>
        {expandedSections[section] ? (
          <ChevronUp className="w-4 h-4" />
        ) : (
          <ChevronDown className="w-4 h-4" />
        )}
      </Button>
      
      <AnimatePresence>
        {expandedSections[section] && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
      
      <Separator />
    </div>
  )

  return (
    <Card className="card-premium">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="w-5 h-5 text-primary" />
            <CardTitle>Job Filters</CardTitle>
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="theme-glow">
                {activeFilterCount}
              </Badge>
            )}
          </div>
          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="w-4 h-4 mr-1" />
              Clear
            </Button>
          )}
        </div>
        <CardDescription>
          Find your perfect job opportunity
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Job Categories Filter */}
        <FilterSection title="Job Categories" icon={Briefcase} section="categories">
          <div className="space-y-4">
            {jobCategories.map((categoryGroup) => (
              <div key={categoryGroup.name} className="space-y-3">
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-primary/15 to-primary/10 border border-primary/20">
                  <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                    <categoryGroup.icon className="w-4 h-4 text-primary-foreground" />
                  </div>
                  <span className="font-semibold text-primary text-sm">{categoryGroup.name}</span>
                </div>
                <div className="flex flex-wrap gap-2 pl-2">
                  {categoryGroup.categories.map((category) => (
                    <Badge
                      key={category}
                      variant={selectedCategories.includes(category) ? "default" : "outline"}
                      className={`cursor-pointer transition-all duration-200 ${
                        selectedCategories.includes(category) 
                          ? 'theme-glow bg-primary text-primary-foreground border-primary' 
                          : 'hover:border-primary/50 hover:bg-primary/5'
                      }`}
                      onClick={() => toggleCategory(category)}
                    >
                      {category}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Job Type Filter */}
        <FilterSection title="Job Type" icon={Clock} section="jobType">
          <div className="space-y-2">
            {jobTypes.map((type) => (
              <div key={type} className="flex items-center space-x-2">
                <Checkbox
                  id={type}
                  checked={selectedJobTypes.includes(type)}
                  onCheckedChange={() => toggleJobType(type)}
                />
                <Label htmlFor={type} className="text-sm font-medium">
                  {type}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Experience Level Filter */}
        <FilterSection title="Experience Level" icon={GraduationCap} section="experience">
          <div className="space-y-2">
            {experienceLevels.map((level) => (
              <div key={level} className="flex items-center space-x-2">
                <Checkbox
                  id={level}
                  checked={selectedExperience.includes(level)}
                  onCheckedChange={() => toggleExperience(level)}
                />
                <Label htmlFor={level} className="text-sm font-medium">
                  {level}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Salary Range Filter */}
        <FilterSection title="Salary Range" icon={DollarSign} section="salary">
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Annual Salary (USD)</span>
              <span className="font-medium">
                ${salaryRange[0].toLocaleString()} - ${salaryRange[1] === 300000 ? '300k+' : salaryRange[1].toLocaleString()}
              </span>
            </div>
            <Slider
              value={salaryRange}
              onValueChange={setSalaryRange}
              max={300000}
              min={0}
              step={5000}
              className="w-full"
            />
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSalaryRange([50000, 100000])}
                className="text-xs"
              >
                $50k - $100k
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSalaryRange([100000, 150000])}
                className="text-xs"
              >
                $100k - $150k
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSalaryRange([150000, 200000])}
                className="text-xs"
              >
                $150k - $200k
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSalaryRange([200000, 300000])}
                className="text-xs"
              >
                $200k+
              </Button>
            </div>
          </div>
        </FilterSection>

        {/* Work Model Filter */}
        <FilterSection title="Work Model" icon={Globe} section="workModel">
          <div className="space-y-2">
            {workModels.map((model) => (
              <div key={model} className="flex items-center space-x-2">
                <Checkbox
                  id={model}
                  checked={selectedWorkModel.includes(model)}
                  onCheckedChange={() => toggleWorkModel(model)}
                />
                <Label htmlFor={model} className="text-sm font-medium">
                  {model}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {showApplyButton && (
          <Button 
            onClick={onFiltersChange} 
            className="w-full button-premium"
          >
            Apply Filters
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
