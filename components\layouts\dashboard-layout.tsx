"use client"

import React from "react"
import { DashboardTopbar } from "@/components/dashboard/dashboard-topbar"
import { DashboardSidebar } from "@/components/dashboard/dashboard-sidebar"
import { BackgroundPattern } from "@/components/background-pattern"
import { NotificationSystem } from "@/components/notification-system"
import { SidebarProvider } from "@/components/ui/sidebar"

interface DashboardLayoutProps {
  children: React.ReactNode
  showBackgroundPattern?: boolean
  showNotificationSystem?: boolean
  className?: string
}

export function DashboardLayout({
  children,
  showBackgroundPattern = true,
  showNotificationSystem = true,
  className = "min-h-screen bg-background"
}: DashboardLayoutProps) {
  return (
    <SidebarProvider>
      <div className={className}>
        {showBackgroundPattern && <BackgroundPattern />}
        
        {/* Dashboard Layout Structure */}
        <div className="flex h-screen overflow-hidden">
          {/* Sidebar */}
          <DashboardSidebar />
          
          {/* Main Content Area */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Top Navigation */}
            <DashboardTopbar />
            
            {/* Main Content */}
            <main className="flex-1 overflow-auto relative">
              <div className="container mx-auto p-6">
                {children}
              </div>
            </main>
          </div>
        </div>
        
        {showNotificationSystem && <NotificationSystem />}
      </div>
    </SidebarProvider>
  )
}
