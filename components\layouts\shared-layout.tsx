"use client"

import React from "react"
import { Navigation } from "@/components/navigation"
import { Footer } from "@/components/footer"
import { BackgroundPattern } from "@/components/background-pattern"
import { FloatingActionButton } from "@/components/floating-action-button"
import { NotificationSystem } from "@/components/notification-system"

interface SharedLayoutProps {
  children: React.ReactNode
  showNavigation?: boolean
  showFooter?: boolean
  showBackgroundPattern?: boolean
  showFloatingActionButton?: boolean
  showNotificationSystem?: boolean
  className?: string
}

export function SharedLayout({
  children,
  showNavigation = true,
  showFooter = true,
  showBackgroundPattern = true,
  showFloatingActionButton = true,
  showNotificationSystem = true,
  className = "min-h-screen bg-background"
}: SharedLayoutProps) {
  return (
    <div className={className}>
      {showBackgroundPattern && <BackgroundPattern />}
      {showNavigation && <Navigation />}
      <main className="relative">
        {children}
      </main>
      {showFooter && <Footer />}
      {showFloatingActionButton && <FloatingActionButton />}
      {showNotificationSystem && <NotificationSystem />}
    </div>
  )
}
