"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Clock, MapPin, DollarSign, Users, TrendingUp, Zap, Eye, Heart, Share2, Bookmark } from "lucide-react"
import { PremiumJobDetailModal } from "@/components/jobs/premium-job-detail-modal"
import { JobApplicationModal } from "@/components/jobs/job-application-modal"
import Link from "next/link"

const liveJobs = [
  {
    id: 1,
    title: "Senior Full Stack Developer",
    company: "TechFlow",
    logo: "/placeholder.svg?height=40&width=40",
    location: "San Francisco, CA",
    salary: "$140k - $180k",
    type: "Full-time",
    posted: "2 minutes ago",
    applicants: 12,
    views: 45,
    tags: ["React", "Node.js", "TypeScript"],
    urgent: true,
    featured: true,
  },
  {
    id: 2,
    title: "Product Designer",
    company: "DesignCorp",
    logo: "/placeholder.svg?height=40&width=40",
    location: "Remote",
    salary: "$100k - $130k",
    type: "Full-time",
    posted: "5 minutes ago",
    applicants: 8,
    views: 32,
    tags: ["Figma", "UI/UX", "Prototyping"],
    urgent: false,
    featured: false,
  },
  {
    id: 3,
    title: "DevOps Engineer",
    company: "CloudTech",
    logo: "/placeholder.svg?height=40&width=40",
    location: "Austin, TX",
    salary: "$120k - $160k",
    type: "Contract",
    posted: "8 minutes ago",
    applicants: 15,
    views: 67,
    tags: ["AWS", "Docker", "Kubernetes"],
    urgent: true,
    featured: false,
  },
]

export function LiveJobFeed() {
  const [jobs, setJobs] = useState(liveJobs)
  const [newJobsCount, setNewJobsCount] = useState(0)
  const [likedJobs, setLikedJobs] = useState<number[]>([])
  const [bookmarkedJobs, setBookmarkedJobs] = useState<number[]>([])
  const [selectedJob, setSelectedJob] = useState<any>(null)
  const [applicationJob, setApplicationJob] = useState<any>(null)

  // Transform live job data to proper job format
  const transformLiveJobToJob = (liveJob: any) => ({
    ...liveJob,
    company: {
      name: liveJob.company,
      logo: liveJob.logo,
      verified: true,
      id: liveJob.id,
      industry: "Technology",
      size: "100-500",
      rating: 4.5
    },
    remote: liveJob.location === "Remote",
    workModel: liveJob.location === "Remote" ? "Remote" : "On-site",
    category: "Technology",
    description: `Join our team as a ${liveJob.title}. We're looking for talented individuals to help us grow.`,
    requirements: liveJob.tags || [],
    responsibilities: ["Develop and maintain applications", "Collaborate with team members", "Write clean, maintainable code"],
    benefits: ["Health insurance", "401k matching", "Flexible hours", "Remote work options"],
    experience: "Mid Level",
    skills: liveJob.tags || [],
    salary: {
      min: parseInt(liveJob.salary.replace(/[^0-9]/g, '').slice(0, 3)) * 1000,
      max: parseInt(liveJob.salary.replace(/[^0-9]/g, '').slice(3, 6)) * 1000,
      currency: "USD",
      period: "year"
    }
  })

  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate new jobs being added
      const shouldAddJob = Math.random() > 0.7
      if (shouldAddJob) {
        setNewJobsCount((prev) => prev + 1)
      }
    }, 10000)

    return () => clearInterval(interval)
  }, [])

  const toggleLike = (jobId: number) => {
    setLikedJobs((prev) => (prev.includes(jobId) ? prev.filter((id) => id !== jobId) : [...prev, jobId]))
  }

  const toggleBookmark = (jobId: number) => {
    setBookmarkedJobs((prev) => (prev.includes(jobId) ? prev.filter((id) => id !== jobId) : [...prev, jobId]))
  }

  const loadNewJobs = () => {
    setNewJobsCount(0)
    // Simulate loading new jobs
  }

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-green-800 text-sm font-medium mb-6">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
            Live Job Feed
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Real-Time <span className="text-primary">Opportunities</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Fresh jobs posted every minute. Be the first to apply and get ahead of the competition.
          </p>
        </motion.div>

        {/* New Jobs Notification */}
        <AnimatePresence>
          {newJobsCount > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50"
            >
              <Button onClick={loadNewJobs} className="bg-green-500 hover:bg-green-600 text-white shadow-lg">
                <TrendingUp className="w-4 h-4 mr-2" />
                {newJobsCount} new job{newJobsCount > 1 ? "s" : ""} available
              </Button>
            </motion.div>
          )}
        </AnimatePresence>

        <div className="max-w-4xl mx-auto space-y-6">
          <AnimatePresence>
            {jobs.map((job, index) => (
              <motion.div
                key={job.id}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -50 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                className="relative"
              >
                <Card
                  className={`glass hover:shadow-xl transition-all duration-300 ${
                    job.featured ? "border-primary/50 bg-primary/5" : "border-border/50"
                  } ${job.urgent ? "ring-2 ring-orange-500/20" : ""}`}
                >
                  {job.urgent && (
                    <div className="absolute -top-2 -right-2">
                      <Badge className="bg-orange-500 text-white animate-pulse">
                        <Zap className="w-3 h-3 mr-1" />
                        Urgent
                      </Badge>
                    </div>
                  )}

                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-4">
                        <Avatar className="w-12 h-12">
                          <AvatarImage src={job.logo || "/placeholder.svg"} alt={job.company} />
                          <AvatarFallback>{job.company[0]}</AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="text-xl font-bold hover:text-primary transition-colors duration-200 cursor-pointer">
                            {job.title}
                          </h3>
                          <p className="text-muted-foreground">{job.company}</p>
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                            <div className="flex items-center space-x-1">
                              <Clock className="w-3 h-3" />
                              <span>{job.posted}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Users className="w-3 h-3" />
                              <span>{job.applicants} applicants</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Eye className="w-3 h-3" />
                              <span>{job.views} views</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => toggleLike(job.id)}
                          className={`p-2 rounded-full transition-colors duration-200 ${
                            likedJobs.includes(job.id) ? "bg-red-100 text-red-500" : "bg-muted hover:bg-muted/80"
                          }`}
                        >
                          <Heart className={`w-4 h-4 ${likedJobs.includes(job.id) ? "fill-current" : ""}`} />
                        </motion.button>
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => toggleBookmark(job.id)}
                          className={`p-2 rounded-full transition-colors duration-200 ${
                            bookmarkedJobs.includes(job.id)
                              ? "bg-primary/10 text-primary"
                              : "bg-muted hover:bg-muted/80"
                          }`}
                        >
                          <Bookmark className={`w-4 h-4 ${bookmarkedJobs.includes(job.id) ? "fill-current" : ""}`} />
                        </motion.button>
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          className="p-2 rounded-full bg-muted hover:bg-muted/80 transition-colors duration-200"
                        >
                          <Share2 className="w-4 h-4" />
                        </motion.button>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <MapPin className="w-4 h-4 text-muted-foreground" />
                        <span>{job.location}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <DollarSign className="w-4 h-4 text-muted-foreground" />
                        <span className="font-semibold text-primary">{job.salary}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4 text-muted-foreground" />
                        <span>{job.type}</span>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2">
                      {job.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t border-border/50">
                      <div className="flex items-center space-x-4">
                        <div className="text-sm text-muted-foreground">
                          Match Score: <span className="font-semibold text-primary">94%</span>
                        </div>
                        {job.featured && (
                          <Badge className="bg-primary/10 text-primary border-primary/20">Featured</Badge>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setApplicationJob(transformLiveJobToJob(job))}
                        >
                          Quick Apply
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setSelectedJob(transformLiveJobToJob(job))}
                        >
                          View Details
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center mt-12"
        >
          <Button size="lg" variant="outline" className="text-lg px-8 bg-transparent" asChild>
            <Link href="/jobs">
              Load More Jobs
            </Link>
          </Button>
        </motion.div>
      </div>

      {/* Job Detail Modal */}
      <PremiumJobDetailModal
        job={selectedJob}
        isOpen={!!selectedJob}
        onClose={() => setSelectedJob(null)}
        onApply={(job) => {
          setApplicationJob(job)
          setSelectedJob(null)
        }}
        onSave={(job) => {
          console.log('Save job:', job.title)
        }}
      />

      {/* Job Application Modal */}
      <JobApplicationModal
        job={applicationJob}
        isOpen={!!applicationJob}
        onClose={() => setApplicationJob(null)}
        onSubmit={(applicationData) => {
          console.log('Application submitted:', applicationData)
          alert(`Application submitted successfully for ${applicationData.jobTitle}!`)
          setApplicationJob(null)
        }}
      />
    </section>
  )
}
