'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { useLocationStore } from '@/stores/location-store'
import { 
  MapPin, 
  X, 
  Shield, 
  Zap, 
  Target,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'

export function LocationBanner() {
  const {
    showLocationBanner,
    isLocationLoading,
    locationError,
    currentLocation,
    requestLocation,
    setLocationPermission,
    dismissLocationBanner
  } = useLocationStore()

  if (!showLocationBanner) return null

  const handleAllowLocation = () => {
    setLocationPermission(true)
  }

  const handleDenyLocation = () => {
    dismissLocationBanner()
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -100 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -100 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        className="fixed top-0 left-0 right-0 z-50"
      >
        {/* Mobile-First Design */}
        <div className="bg-gradient-to-r from-primary/95 via-primary to-primary/95 backdrop-blur-sm border-b border-primary/20 shadow-xl">
          <div className="container mx-auto px-4 py-3 md:py-4">
            <div className="flex items-center justify-between">
              {/* Mobile: Minimal content */}
              <div className="flex items-center space-x-3 flex-1 md:hidden">
                <div className="p-2 bg-white/20 rounded-full">
                  <MapPin className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-white font-medium text-sm truncate">
                    {currentLocation
                      ? `Jobs in ${currentLocation.city}`
                      : 'Find local jobs'
                    }
                  </p>
                  {!currentLocation && (
                    <p className="text-white/80 text-xs">
                      Tap to allow location
                    </p>
                  )}
                </div>
              </div>

              {/* Desktop: Full content */}
              <div className="hidden md:flex items-start space-x-4 flex-1">
                <div className="p-3 bg-white/20 rounded-full">
                  <MapPin className="w-6 h-6 text-white" />
                </div>

                <div className="flex-1">
                  <h3 className="text-xl font-bold mb-2 text-white">
                    Discover Jobs & Talent in Your Area
                  </h3>
                  <p className="text-white/90 mb-4 leading-relaxed">
                    Allow location access to see the most relevant opportunities near you.
                    We'll show jobs prioritized by proximity to maximize your success.
                  </p>

                  {/* Benefits - Desktop only */}
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    <div className="flex items-center space-x-2">
                      <Target className="w-4 h-4 text-white/80" />
                      <span className="text-sm font-medium text-white/90">Local First</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Zap className="w-4 h-4 text-white/80" />
                      <span className="text-sm font-medium text-white/90">Instant Results</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Shield className="w-4 h-4 text-white/80" />
                      <span className="text-sm font-medium text-white/90">Privacy Safe</span>
                    </div>
                  </div>

                  {/* Current Status - Desktop */}
                  {isLocationLoading && (
                    <div className="flex items-center space-x-2 mb-4 p-3 bg-white/20 rounded-lg">
                      <Loader2 className="w-4 h-4 animate-spin text-white" />
                      <span className="text-sm text-white">
                        Detecting your location...
                      </span>
                    </div>
                  )}

                  {locationError && (
                    <div className="flex items-center space-x-2 mb-4 p-3 bg-red-500/20 rounded-lg">
                      <AlertCircle className="w-4 h-4 text-white" />
                      <span className="text-sm text-white">
                        {locationError}
                      </span>
                    </div>
                  )}

                  {currentLocation && (
                    <div className="flex items-center space-x-2 mb-4 p-3 bg-white/20 rounded-lg">
                      <CheckCircle className="w-4 h-4 text-white" />
                      <span className="text-sm text-white">
                        Location: {currentLocation.city}, {currentLocation.country}
                      </span>
                    </div>
                  )}

                  {/* Action Buttons - Desktop */}
                  <div className="flex gap-3">
                    <Button
                      onClick={handleAllowLocation}
                      disabled={isLocationLoading}
                      variant="secondary"
                      className="bg-white text-primary hover:bg-white/90"
                    >
                      {isLocationLoading ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Detecting...
                        </>
                      ) : (
                        <>
                          <MapPin className="w-4 h-4 mr-2" />
                          Allow Location
                        </>
                      )}
                    </Button>

                    <Button
                      variant="ghost"
                      onClick={handleDenyLocation}
                      disabled={isLocationLoading}
                      className="text-white hover:bg-white/20"
                    >
                      Skip
                    </Button>
                  </div>
                </div>
              </div>

              {/* Mobile: Action Buttons */}
              <div className="flex items-center space-x-2 md:hidden">
                {!currentLocation && (
                  <Button
                    size="sm"
                    onClick={handleAllowLocation}
                    disabled={isLocationLoading}
                    variant="secondary"
                    className="bg-white text-primary hover:bg-white/90 text-xs px-3"
                  >
                    {isLocationLoading ? (
                      <Loader2 className="w-3 h-3 animate-spin" />
                    ) : (
                      'Allow'
                    )}
                  </Button>
                )}

                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleDenyLocation}
                  className="text-white hover:bg-white/20 p-1"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {/* Desktop: Close Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDenyLocation}
                disabled={isLocationLoading}
                className="hidden md:flex ml-4 text-white hover:bg-white/20"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}

// Compact version for mobile or smaller spaces
export function LocationBannerCompact() {
  const {
    showLocationBanner,
    isLocationLoading,
    currentLocation,
    requestLocation,
    setLocationPermission,
    dismissLocationBanner
  } = useLocationStore()

  if (!showLocationBanner) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        transition={{ duration: 0.3 }}
        className="fixed top-0 left-0 right-0 z-50 bg-primary/95 backdrop-blur-sm border-b border-primary/20"
      >
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <MapPin className="w-5 h-5 text-white" />
              <div>
                <p className="text-white font-medium text-sm">
                  {currentLocation 
                    ? `Jobs in ${currentLocation.city}, ${currentLocation.country}`
                    : 'Find jobs near you'
                  }
                </p>
                {!currentLocation && (
                  <p className="text-white/80 text-xs">
                    Allow location for personalized results
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {!currentLocation && (
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => setLocationPermission(true)}
                  disabled={isLocationLoading}
                  className="text-xs"
                >
                  {isLocationLoading ? (
                    <Loader2 className="w-3 h-3 animate-spin" />
                  ) : (
                    'Allow'
                  )}
                </Button>
              )}
              
              <Button
                size="sm"
                variant="ghost"
                onClick={dismissLocationBanner}
                className="text-white hover:text-white/80"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}
