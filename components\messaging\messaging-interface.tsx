'use client'

import React, { useState, useEffect, useRef } from 'react'
import { useMessagesStore, useAuthStore, type Conversation, type Message } from '@/stores'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { ButtonLoading } from '@/components/ui/button-loading'
import { 
  Send, 
  MessageSquare, 
  User, 
  Building,
  Calendar,
  Clock,
  CheckCircle,
  Archive,
  MoreHorizontal
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'

interface MessagingInterfaceProps {
  applicationId?: string
  className?: string
}

export function MessagingInterface({ applicationId, className }: MessagingInterfaceProps) {
  const { user } = useAuthStore()
  const {
    conversations,
    currentConversation,
    messages,
    conversationsLoading,
    messagesLoading,
    sendLoading,
    getConversations,
    getConversation,
    getMessages,
    sendMessage,
    markAsRead,
    archiveConversation,
    createConversation,
    setCurrentConversation,
    error,
    clearError
  } = useMessagesStore()

  const [messageText, setMessageText] = useState('')
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Load conversations on mount
  useEffect(() => {
    getConversations()
  }, [getConversations])

  // Auto-select conversation if applicationId provided
  useEffect(() => {
    if (applicationId && conversations.length > 0) {
      const conversation = conversations.find(c => c.applicationId === applicationId)
      if (conversation) {
        handleSelectConversation(conversation)
      }
    }
  }, [applicationId, conversations])

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Handle conversation selection
  const handleSelectConversation = async (conversation: Conversation) => {
    setSelectedConversationId(conversation._id)
    setCurrentConversation(conversation)
    
    try {
      await getMessages(conversation._id)
      if (conversation.unreadCount > 0) {
        await markAsRead(conversation._id)
      }
    } catch (error) {
      console.error('Failed to load conversation:', error)
    }
  }

  // Handle send message
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!messageText.trim() || !currentConversation) return

    try {
      await sendMessage(currentConversation._id, messageText.trim())
      setMessageText('')
    } catch (error) {
      console.error('Failed to send message:', error)
    }
  }

  // Handle archive conversation
  const handleArchiveConversation = async (conversationId: string) => {
    try {
      await archiveConversation(conversationId)
      if (selectedConversationId === conversationId) {
        setSelectedConversationId(null)
        setCurrentConversation(null)
      }
    } catch (error) {
      console.error('Failed to archive conversation:', error)
    }
  }

  // Format message time
  const formatMessageTime = (date: Date) => {
    const now = new Date()
    const messageDate = new Date(date)
    const diffInHours = (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60)
    
    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else {
      return messageDate.toLocaleDateString()
    }
  }

  // Get message display info
  const getMessageInfo = (message: Message) => {
    const isFromCurrentUser = message.senderId === user?._id
    const isSystemMessage = message.type === 'system' || message.type === 'status_update'
    
    return { isFromCurrentUser, isSystemMessage }
  }

  return (
    <div className={cn('h-[600px] flex border rounded-lg overflow-hidden', className)}>
      {/* Conversations List */}
      <div className="w-1/3 border-r bg-muted/20">
        <div className="p-4 border-b">
          <h3 className="font-semibold flex items-center space-x-2">
            <MessageSquare className="w-5 h-5" />
            <span>Messages</span>
          </h3>
        </div>
        
        <ScrollArea className="h-[calc(600px-73px)]">
          {conversationsLoading ? (
            <div className="p-4 space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-16 bg-muted rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : conversations.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">
              <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No conversations yet</p>
            </div>
          ) : (
            <div className="p-2">
              {conversations.map((conversation) => (
                <div
                  key={conversation._id}
                  onClick={() => handleSelectConversation(conversation)}
                  className={cn(
                    'p-3 rounded-lg cursor-pointer transition-colors mb-2',
                    selectedConversationId === conversation._id
                      ? 'bg-primary/10 border border-primary/20'
                      : 'hover:bg-muted/50'
                  )}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1 min-w-0">
                      {/* Avatar */}
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold flex-shrink-0">
                        {user?.role === 'job_seeker' 
                          ? conversation.participants.recruiter.name.slice(0, 2).toUpperCase()
                          : conversation.participants.candidate.name.slice(0, 2).toUpperCase()
                        }
                      </div>
                      
                      {/* Conversation Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <p className="font-medium text-sm truncate">
                            {user?.role === 'job_seeker' 
                              ? conversation.participants.recruiter.name
                              : conversation.participants.candidate.name
                            }
                          </p>
                          {conversation.unreadCount > 0 && (
                            <Badge variant="destructive" className="text-xs">
                              {conversation.unreadCount}
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-xs text-muted-foreground truncate mb-1">
                          {conversation.job.title} at {conversation.job.company.name}
                        </p>
                        
                        {conversation.lastMessage && (
                          <p className="text-xs text-muted-foreground truncate">
                            {conversation.lastMessage.content}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    {/* Actions */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                          <MoreHorizontal className="w-3 h-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => markAsRead(conversation._id)}>
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Mark as Read
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => handleArchiveConversation(conversation._id)}
                          className="text-red-600 focus:text-red-600"
                        >
                          <Archive className="w-4 h-4 mr-2" />
                          Archive
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </div>

      {/* Messages Area */}
      <div className="flex-1 flex flex-col">
        {currentConversation ? (
          <>
            {/* Conversation Header */}
            <div className="p-4 border-b bg-background">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                    {user?.role === 'job_seeker' 
                      ? currentConversation.participants.recruiter.name.slice(0, 2).toUpperCase()
                      : currentConversation.participants.candidate.name.slice(0, 2).toUpperCase()
                    }
                  </div>
                  <div>
                    <h4 className="font-semibold">
                      {user?.role === 'job_seeker' 
                        ? currentConversation.participants.recruiter.name
                        : currentConversation.participants.candidate.name
                      }
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {currentConversation.job.title} at {currentConversation.job.company.name}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Messages */}
            <ScrollArea className="flex-1 p-4">
              {messagesLoading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-12 bg-muted rounded-lg max-w-xs"></div>
                    </div>
                  ))}
                </div>
              ) : messages.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No messages yet. Start the conversation!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map((message) => {
                    const { isFromCurrentUser, isSystemMessage } = getMessageInfo(message)
                    
                    if (isSystemMessage) {
                      return (
                        <div key={message._id} className="text-center">
                          <div className="inline-flex items-center space-x-2 bg-muted px-3 py-1 rounded-full text-xs text-muted-foreground">
                            <Clock className="w-3 h-3" />
                            <span>{message.content}</span>
                            <span>•</span>
                            <span>{formatMessageTime(message.createdAt)}</span>
                          </div>
                        </div>
                      )
                    }
                    
                    return (
                      <div
                        key={message._id}
                        className={cn(
                          'flex',
                          isFromCurrentUser ? 'justify-end' : 'justify-start'
                        )}
                      >
                        <div
                          className={cn(
                            'max-w-xs lg:max-w-md px-4 py-2 rounded-lg',
                            isFromCurrentUser
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-muted'
                          )}
                        >
                          <p className="text-sm">{message.content}</p>
                          <p className={cn(
                            'text-xs mt-1',
                            isFromCurrentUser 
                              ? 'text-primary-foreground/70' 
                              : 'text-muted-foreground'
                          )}>
                            {formatMessageTime(message.createdAt)}
                          </p>
                        </div>
                      </div>
                    )
                  })}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </ScrollArea>

            {/* Message Input */}
            <form onSubmit={handleSendMessage} className="p-4 border-t">
              <div className="flex space-x-2">
                <Input
                  placeholder="Type your message..."
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  disabled={sendLoading}
                  className="flex-1"
                />
                <ButtonLoading
                  type="submit"
                  loading={sendLoading}
                  disabled={!messageText.trim() || sendLoading}
                  size="sm"
                >
                  <Send className="w-4 h-4" />
                </ButtonLoading>
              </div>
            </form>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Select a conversation to start messaging</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
