'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useApplicationsStore, type Application } from '@/stores'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ButtonLoading } from '@/components/ui/button-loading'
import { 
  Search, 
  Filter, 
  Calendar,
  User,
  Mail,
  Phone,
  FileText,
  ExternalLink,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock,
  MessageSquare,
  Download,
  Star,
  StarOff
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { cn } from '@/lib/utils'

interface ApplicationManagementProps {
  jobId?: string
  companyId: string
  className?: string
}

interface ApplicationFilters {
  status: string
  dateRange: string
  search: string
  starred: boolean
}

export function ApplicationManagement({ jobId, companyId, className }: ApplicationManagementProps) {
  const router = useRouter()
  const {
    applications,
    getApplicationsByCompany,
    getApplicationsByJob,
    updateApplicationStatus,
    starApplication,
    unstarApplication,
    applicationsLoading,
    updateLoading,
    error,
    clearApplicationsError
  } = useApplicationsStore()

  const [filters, setFilters] = useState<ApplicationFilters>({
    status: 'all',
    dateRange: 'all',
    search: '',
    starred: false
  })

  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null)
  const [showStatusDialog, setShowStatusDialog] = useState(false)
  const [newStatus, setNewStatus] = useState<Application['status'] | ''>('')
  const [activeTab, setActiveTab] = useState('all')

  // Load applications
  useEffect(() => {
    if (jobId) {
      getApplicationsByJob(jobId)
    } else {
      getApplicationsByCompany(companyId)
    }
  }, [jobId, companyId, getApplicationsByJob, getApplicationsByCompany])

  // Filter applications
  const getFilteredApplications = () => {
    let filtered = applications

    // Filter by search
    if (filters.search) {
      filtered = filtered.filter(app =>
        app.job?.title.toLowerCase().includes(filters.search.toLowerCase()) ||
        app.userId.toLowerCase().includes(filters.search.toLowerCase()) // Would be user name in real app
      )
    }

    // Filter by status
    if (filters.status !== 'all') {
      filtered = filtered.filter(app => app.status === filters.status)
    }

    // Filter by starred
    if (filters.starred) {
      filtered = filtered.filter(app => app.starred)
    }

    // Filter by date range
    if (filters.dateRange !== 'all') {
      const now = new Date()
      let dateThreshold: Date

      switch (filters.dateRange) {
        case '24h':
          dateThreshold = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          break
        case '7d':
          dateThreshold = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          dateThreshold = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        default:
          dateThreshold = new Date(0)
      }

      filtered = filtered.filter(app => new Date(app.submittedAt) >= dateThreshold)
    }

    return filtered
  }

  const filteredApplications = getFilteredApplications()

  // Group applications by status for tabs
  const applicationsByStatus = {
    all: filteredApplications,
    pending: filteredApplications.filter(app => ['submitted', 'under_review'].includes(app.status)),
    interviews: filteredApplications.filter(app => ['interview_scheduled', 'interviewed'].includes(app.status)),
    offers: filteredApplications.filter(app => app.status === 'offer_extended'),
    rejected: filteredApplications.filter(app => app.status === 'rejected')
  }

  // Handle status update
  const handleStatusUpdate = async () => {
    if (!selectedApplication || !newStatus) return

    try {
      await updateApplicationStatus(selectedApplication._id, newStatus)
      setShowStatusDialog(false)
      setSelectedApplication(null)
      setNewStatus('')
    } catch (error) {
      console.error('Failed to update application status:', error)
    }
  }

  // Handle star toggle
  const handleStarToggle = async (application: Application) => {
    try {
      if (application.starred) {
        await unstarApplication(application._id)
      } else {
        await starApplication(application._id)
      }
    } catch (error) {
      console.error('Failed to toggle star:', error)
    }
  }

  // Get status display
  const getStatusDisplay = (status: Application['status']) => {
    const statusMap = {
      submitted: { label: 'New', color: 'bg-blue-100 text-blue-800 border-blue-200' },
      under_review: { label: 'Reviewing', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
      interview_scheduled: { label: 'Interview Scheduled', color: 'bg-purple-100 text-purple-800 border-purple-200' },
      interviewed: { label: 'Interviewed', color: 'bg-indigo-100 text-indigo-800 border-indigo-200' },
      offer_extended: { label: 'Offer Extended', color: 'bg-green-100 text-green-800 border-green-200' },
      rejected: { label: 'Rejected', color: 'bg-red-100 text-red-800 border-red-200' },
      withdrawn: { label: 'Withdrawn', color: 'bg-gray-100 text-gray-800 border-gray-200' }
    }
    return statusMap[status] || statusMap.submitted
  }

  // Format date
  const formatDate = (date: Date) => {
    const now = new Date()
    const diffInDays = Math.floor((now.getTime() - new Date(date).getTime()) / (1000 * 60 * 60 * 24))
    
    if (diffInDays === 0) return 'Today'
    if (diffInDays === 1) return 'Yesterday'
    if (diffInDays < 7) return `${diffInDays} days ago`
    return new Date(date).toLocaleDateString()
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">
            {jobId ? 'Job Applications' : 'All Applications'}
          </h2>
          <p className="text-muted-foreground">
            Review and manage candidate applications
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="w-5 h-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  id="search"
                  placeholder="Search applications..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div>
              <Label>Status</Label>
              <Select
                value={filters.status}
                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="submitted">New</SelectItem>
                  <SelectItem value="under_review">Under Review</SelectItem>
                  <SelectItem value="interview_scheduled">Interview Scheduled</SelectItem>
                  <SelectItem value="interviewed">Interviewed</SelectItem>
                  <SelectItem value="offer_extended">Offer Extended</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date Range */}
            <div>
              <Label>Date Range</Label>
              <Select
                value={filters.dateRange}
                onValueChange={(value) => setFilters(prev => ({ ...prev, dateRange: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="24h">Last 24 hours</SelectItem>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Clear Filters */}
            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => setFilters({
                  status: 'all',
                  dateRange: 'all',
                  search: '',
                  starred: false
                })}
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Applications Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">
            All ({applicationsByStatus.all.length})
          </TabsTrigger>
          <TabsTrigger value="pending">
            Pending ({applicationsByStatus.pending.length})
          </TabsTrigger>
          <TabsTrigger value="interviews">
            Interviews ({applicationsByStatus.interviews.length})
          </TabsTrigger>
          <TabsTrigger value="offers">
            Offers ({applicationsByStatus.offers.length})
          </TabsTrigger>
          <TabsTrigger value="rejected">
            Rejected ({applicationsByStatus.rejected.length})
          </TabsTrigger>
        </TabsList>

        {Object.entries(applicationsByStatus).map(([status, apps]) => (
          <TabsContent key={status} value={status} className="mt-6">
            {apps.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <User className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Applications</h3>
                  <p className="text-muted-foreground">
                    {status === 'all' 
                      ? 'No applications match your current filters.'
                      : `No applications in ${status} status.`
                    }
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {apps.map((application) => (
                  <ApplicationCard
                    key={application._id}
                    application={application}
                    onStatusUpdate={(newStatus) => {
                      setSelectedApplication(application)
                      setNewStatus(newStatus)
                      setShowStatusDialog(true)
                    }}
                    onStarToggle={() => handleStarToggle(application)}
                    onView={() => router.push(`/applications/${application._id}`)}
                    showActions
                  />
                ))}
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>

      {/* Status Update Dialog */}
      <AlertDialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Update Application Status</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to update this application status to{' '}
              <strong>{newStatus?.replace('_', ' ')}</strong>?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <ButtonLoading
              loading={updateLoading}
              onClick={handleStatusUpdate}
            >
              Update Status
            </ButtonLoading>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

// Enhanced Application Card for Recruiters
interface ApplicationCardProps {
  application: Application
  onStatusUpdate?: (status: Application['status']) => void
  onStarToggle?: () => void
  onView?: () => void
  showActions?: boolean
}

function ApplicationCard({ 
  application, 
  onStatusUpdate, 
  onStarToggle, 
  onView,
  showActions = false 
}: ApplicationCardProps) {
  const statusDisplay = getStatusDisplay(application.status)

  const getStatusDisplay = (status: Application['status']) => {
    const statusMap = {
      submitted: { label: 'New', color: 'bg-blue-100 text-blue-800 border-blue-200' },
      under_review: { label: 'Reviewing', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
      interview_scheduled: { label: 'Interview Scheduled', color: 'bg-purple-100 text-purple-800 border-purple-200' },
      interviewed: { label: 'Interviewed', color: 'bg-indigo-100 text-indigo-800 border-indigo-200' },
      offer_extended: { label: 'Offer Extended', color: 'bg-green-100 text-green-800 border-green-200' },
      rejected: { label: 'Rejected', color: 'bg-red-100 text-red-800 border-red-200' },
      withdrawn: { label: 'Withdrawn', color: 'bg-gray-100 text-gray-800 border-gray-200' }
    }
    return statusMap[status] || statusMap.submitted
  }

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffInDays = Math.floor((now.getTime() - new Date(date).getTime()) / (1000 * 60 * 60 * 24))
    
    if (diffInDays === 0) return 'Today'
    if (diffInDays === 1) return 'Yesterday'
    if (diffInDays < 7) return `${diffInDays} days ago`
    return new Date(date).toLocaleDateString()
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4 flex-1">
            {/* Candidate Avatar */}
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
              {application.userId.slice(0, 2).toUpperCase()}
            </div>

            {/* Application Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="font-semibold text-lg">Candidate #{application.userId.slice(-6)}</h3>
                {application.starred && (
                  <Star className="w-4 h-4 text-yellow-500 fill-current" />
                )}
              </div>
              
              <p className="text-muted-foreground mb-2">{application.job?.title}</p>
              
              <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-3">
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4" />
                  <span>Applied {formatDate(application.submittedAt)}</span>
                </div>
                
                {application.customFields?.availableStartDate && (
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>Available {new Date(application.customFields.availableStartDate).toLocaleDateString()}</span>
                  </div>
                )}
              </div>

              {/* Cover Letter Preview */}
              <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                {application.coverLetter}
              </p>

              {/* Quick Actions */}
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" onClick={onView}>
                  <FileText className="w-4 h-4 mr-1" />
                  View Details
                </Button>
                
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-1" />
                  Resume
                </Button>
                
                {application.customFields?.portfolioUrl && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={application.customFields.portfolioUrl} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="w-4 h-4 mr-1" />
                      Portfolio
                    </a>
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Status & Actions */}
          <div className="flex items-start space-x-2 ml-4">
            <Badge className={statusDisplay.color} variant="outline">
              {statusDisplay.label}
            </Badge>
            
            {showActions && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={onView}>
                    <FileText className="w-4 h-4 mr-2" />
                    View Application
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem onClick={onStarToggle}>
                    {application.starred ? (
                      <>
                        <StarOff className="w-4 h-4 mr-2" />
                        Remove Star
                      </>
                    ) : (
                      <>
                        <Star className="w-4 h-4 mr-2" />
                        Add Star
                      </>
                    )}
                  </DropdownMenuItem>
                  
                  <DropdownMenuSeparator />
                  
                  <DropdownMenuItem onClick={() => onStatusUpdate?.('under_review')}>
                    <Clock className="w-4 h-4 mr-2" />
                    Mark as Reviewing
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem onClick={() => onStatusUpdate?.('interview_scheduled')}>
                    <Calendar className="w-4 h-4 mr-2" />
                    Schedule Interview
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem onClick={() => onStatusUpdate?.('offer_extended')}>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Extend Offer
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem 
                    onClick={() => onStatusUpdate?.('rejected')}
                    className="text-red-600 focus:text-red-600"
                  >
                    <XCircle className="w-4 h-4 mr-2" />
                    Reject Application
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
