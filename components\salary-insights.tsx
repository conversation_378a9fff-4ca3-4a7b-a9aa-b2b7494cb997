"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from "recharts"
import { TrendingUp, DollarSign, MapPin, Briefcase, Users, Award, Target, ChevronRight } from "lucide-react"

const salaryData = [
  { role: "Frontend Dev", min: 80, avg: 110, max: 140, demand: 95 },
  { role: "Backend Dev", min: 85, avg: 115, max: 145, demand: 88 },
  { role: "Full Stack", min: 90, avg: 125, max: 160, demand: 92 },
  { role: "DevOps", min: 95, avg: 130, max: 165, demand: 85 },
  { role: "Data Scientist", min: 100, avg: 135, max: 170, demand: 78 },
  { role: "Product Manager", min: 105, avg: 140, max: 180, demand: 82 },
]

const trendData = [
  { month: "Jan", salary: 115 },
  { month: "Feb", salary: 118 },
  { month: "Mar", salary: 122 },
  { month: "Apr", salary: 125 },
  { month: "May", salary: 128 },
  { month: "Jun", salary: 132 },
]

const locationData = [
  { city: "San Francisco", avg: 165, cost: 95 },
  { city: "New York", avg: 155, cost: 90 },
  { city: "Seattle", avg: 145, cost: 75 },
  { city: "Austin", avg: 125, cost: 65 },
  { city: "Denver", avg: 115, cost: 60 },
  { city: "Remote", avg: 120, cost: 50 },
]

export function SalaryInsights() {
  const [selectedRole, setSelectedRole] = useState("Full Stack")
  const [activeTab, setActiveTab] = useState("overview")

  const selectedRoleData = salaryData.find((role) => role.role === selectedRole) || salaryData[2]

  return (
    <section className="py-20 bg-gradient-to-br from-primary/5 via-background to-primary/10">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
            <DollarSign className="w-4 h-4 mr-2" />
            Salary Intelligence
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Know Your <span className="text-primary">Worth</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Real-time salary data and market insights to help you negotiate better and make informed career decisions.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Role Selection */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <Card className="glass h-full">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Briefcase className="w-5 h-5 text-primary" />
                  <span>Select Role</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {salaryData.map((role) => (
                  <motion.button
                    key={role.role}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setSelectedRole(role.role)}
                    className={`w-full p-4 rounded-lg text-left transition-all duration-200 ${
                      selectedRole === role.role
                        ? "bg-primary text-primary-foreground shadow-lg"
                        : "bg-muted/50 hover:bg-muted"
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-semibold">{role.role}</div>
                        <div
                          className={`text-sm ${
                            selectedRole === role.role ? "text-primary-foreground/80" : "text-muted-foreground"
                          }`}
                        >
                          ${role.avg}k average
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div
                          className={`text-xs px-2 py-1 rounded ${
                            selectedRole === role.role
                              ? "bg-primary-foreground/20 text-primary-foreground"
                              : "bg-primary/10 text-primary"
                          }`}
                        >
                          {role.demand}% demand
                        </div>
                        <ChevronRight className="w-4 h-4" />
                      </div>
                    </div>
                  </motion.button>
                ))}
              </CardContent>
            </Card>
          </motion.div>

          {/* Salary Overview */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Card className="glass h-full">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Target className="w-5 h-5 text-primary" />
                    <span>{selectedRole} Salary Range</span>
                  </div>
                  <Badge className="bg-green-100 text-green-800">
                    <TrendingUp className="w-3 h-3 mr-1" />
                    +12% YoY
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-8">
                <div className="grid grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-muted-foreground mb-2">${selectedRoleData.min}k</div>
                    <div className="text-sm text-muted-foreground">Entry Level</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary mb-2">${selectedRoleData.avg}k</div>
                    <div className="text-sm text-muted-foreground">Average</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">${selectedRoleData.max}k</div>
                    <div className="text-sm text-muted-foreground">Senior Level</div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex justify-between text-sm">
                    <span>Market Demand</span>
                    <span>{selectedRoleData.demand}%</span>
                  </div>
                  <Progress value={selectedRoleData.demand} className="h-3" />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 rounded-lg bg-muted/30">
                    <div className="flex items-center space-x-2 mb-2">
                      <Users className="w-4 h-4 text-primary" />
                      <span className="text-sm font-medium">Job Openings</span>
                    </div>
                    <div className="text-2xl font-bold">2,847</div>
                    <div className="text-xs text-green-600">+23% this month</div>
                  </div>
                  <div className="p-4 rounded-lg bg-muted/30">
                    <div className="flex items-center space-x-2 mb-2">
                      <Award className="w-4 h-4 text-primary" />
                      <span className="text-sm font-medium">Skill Premium</span>
                    </div>
                    <div className="text-2xl font-bold">+18%</div>
                    <div className="text-xs text-muted-foreground">vs general market</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Detailed Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Salary Trends */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="glass">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5 text-primary" />
                  <span>Salary Trends (6 months)</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <AreaChart data={trendData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                    <XAxis dataKey="month" stroke="hsl(var(--muted-foreground))" />
                    <YAxis stroke="hsl(var(--muted-foreground))" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "hsl(var(--background))",
                        border: "1px solid hsl(var(--border))",
                        borderRadius: "8px",
                      }}
                    />
                    <Area
                      type="monotone"
                      dataKey="salary"
                      stroke="hsl(var(--primary))"
                      fill="hsl(var(--primary))"
                      fillOpacity={0.2}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </motion.div>

          {/* Location Comparison */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="glass">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="w-5 h-5 text-primary" />
                  <span>Location Comparison</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={locationData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                    <XAxis dataKey="city" stroke="hsl(var(--muted-foreground))" />
                    <YAxis stroke="hsl(var(--muted-foreground))" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "hsl(var(--background))",
                        border: "1px solid hsl(var(--border))",
                        borderRadius: "8px",
                      }}
                    />
                    <Bar dataKey="avg" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="text-center mt-12"
        >
          <Button size="lg" className="text-lg px-8">
            Get Personalized Salary Report
          </Button>
        </motion.div>
      </div>
    </section>
  )
}
