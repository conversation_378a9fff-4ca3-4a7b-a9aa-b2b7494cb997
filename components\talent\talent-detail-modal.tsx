'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { 
  MapPin, 
  Star, 
  DollarSign, 
  Clock, 
  Heart, 
  MessageCircle,
  Briefcase,
  CheckCircle,
  TrendingUp,
  Award,
  Users,
  Calendar,
  Globe,
  Mail,
  Phone,
  ExternalLink,
  Download,
  Share2,
  Flag,
  X,
  ChevronRight,
  Building,
  GraduationCap,
  Code,
  Palette,
  Database
} from 'lucide-react'

interface Talent {
  id: number
  name: string
  title: string
  location: string
  avatar: string
  rating: number
  experience: number
  hourlyRate: number
  skills: string[]
  bio: string
  availability: string
  projects: number
  reviews: number
  responseTime: string
  successRate: number
  portfolio?: any[]
  languages?: string[]
  timezone?: string
  education?: any[]
  workHistory?: any[]
  certifications?: any[]
}

interface TalentDetailModalProps {
  talent: Talent | null
  isOpen: boolean
  onClose: () => void
  onContact: (talent: Talent) => void
  onHire: (talent: Talent) => void
}

export function TalentDetailModal({ 
  talent, 
  isOpen, 
  onClose, 
  onContact, 
  onHire 
}: TalentDetailModalProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [isSaved, setIsSaved] = useState(false)

  if (!talent) return null

  const getAvailabilityColor = (availability: string) => {
    switch (availability.toLowerCase()) {
      case 'available':
        return 'bg-green-500'
      case 'busy':
        return 'bg-yellow-500'
      case 'unavailable':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getExperienceLevel = (years: number) => {
    if (years < 2) return 'Junior'
    if (years < 5) return 'Mid-level'
    if (years < 8) return 'Senior'
    return 'Expert'
  }

  const mockPortfolio = [
    {
      id: 1,
      title: "E-commerce Platform Redesign",
      description: "Complete redesign of a major e-commerce platform with 2M+ users",
      image: "/api/placeholder/300/200",
      technologies: ["React", "Node.js", "PostgreSQL"],
      link: "https://example.com"
    },
    {
      id: 2,
      title: "Mobile Banking App",
      description: "Secure mobile banking application with biometric authentication",
      image: "/api/placeholder/300/200",
      technologies: ["React Native", "Firebase", "Stripe"],
      link: "https://example.com"
    }
  ]

  const mockWorkHistory = [
    {
      company: "TechCorp Inc.",
      position: "Senior Full Stack Developer",
      duration: "2021 - Present",
      description: "Led development of microservices architecture serving 1M+ users"
    },
    {
      company: "StartupXYZ",
      position: "Full Stack Developer",
      duration: "2019 - 2021",
      description: "Built MVP and scaled to 100k users, implemented CI/CD pipeline"
    }
  ]

  const mockEducation = [
    {
      institution: "Stanford University",
      degree: "Master of Computer Science",
      year: "2019",
      gpa: "3.8/4.0"
    },
    {
      institution: "UC Berkeley",
      degree: "Bachelor of Computer Science",
      year: "2017",
      gpa: "3.6/4.0"
    }
  ]

  const mockReviews = [
    {
      id: 1,
      client: "John Smith",
      company: "TechStartup Inc.",
      rating: 5,
      comment: "Exceptional work quality and communication. Delivered ahead of schedule!",
      project: "E-commerce Platform",
      date: "2 weeks ago"
    },
    {
      id: 2,
      client: "Sarah Johnson",
      company: "Digital Agency",
      rating: 5,
      comment: "Highly skilled developer with great attention to detail.",
      project: "Mobile App Development",
      date: "1 month ago"
    }
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 flex flex-col">
        <div className="flex flex-col h-full max-h-[90vh]">
          {/* Header */}
          <div className="p-6 border-b bg-gradient-to-r from-primary/5 to-primary/10">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="relative">
                  <Avatar className="w-20 h-20 border-2 border-primary/20">
                    <AvatarImage src={talent.avatar} alt={talent.name} />
                    <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-xl">
                      {talent.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className={`absolute -bottom-1 -right-1 w-6 h-6 ${getAvailabilityColor(talent.availability)} rounded-full border-2 border-background flex items-center justify-center`}>
                    <div className="w-2 h-2 bg-white rounded-full" />
                  </div>
                </div>
                
                <div className="flex-1">
                  <h1 className="text-2xl font-bold mb-1">{talent.name}</h1>
                  <p className="text-primary font-medium text-lg mb-2">{talent.title}</p>
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-3">
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-4 h-4" />
                      <span>{talent.location}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span>{talent.rating}</span>
                      <span>({talent.reviews} reviews)</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Briefcase className="w-4 h-4" />
                      <span>{talent.experience} years • {getExperienceLevel(talent.experience)}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className={`${getAvailabilityColor(talent.availability)} text-white`}>
                      {talent.availability}
                    </Badge>
                    <Badge variant="outline">
                      Responds in {talent.responseTime}
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <div className="text-right mr-4">
                  <div className="text-3xl font-bold text-primary">${talent.hourlyRate}</div>
                  <div className="text-sm text-muted-foreground">per hour</div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsSaved(!isSaved)}
                  className={`p-2 ${isSaved ? 'text-red-500' : 'text-muted-foreground'}`}
                >
                  <Heart className={`w-5 h-5 ${isSaved ? 'fill-current' : ''}`} />
                </Button>
                <Button variant="ghost" size="sm" className="p-2">
                  <Share2 className="w-5 h-5" />
                </Button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3 mt-4">
              <Button
                className="button-premium flex-1"
                onClick={() => onHire(talent)}
              >
                <Briefcase className="w-4 h-4 mr-2" />
                Hire Now
              </Button>
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => onContact(talent)}
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Send Message
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  // Navigate to full profile page
                  window.open(`/talent/${talent.id}`, '_blank')
                }}
                className="button-enhanced"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                View Full Profile
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto min-h-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-5 sticky top-0 bg-background z-10 shrink-0">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="portfolio">Portfolio</TabsTrigger>
                <TabsTrigger value="experience">Experience</TabsTrigger>
                <TabsTrigger value="reviews">Reviews</TabsTrigger>
                <TabsTrigger value="contact">Contact</TabsTrigger>
              </TabsList>

              <div className="p-6 flex-1 overflow-y-auto">
                <TabsContent value="overview" className="space-y-6">
                  {/* Bio */}
                  <Card className="card-enhanced">
                    <CardHeader>
                      <CardTitle>About</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground leading-relaxed">{talent.bio}</p>
                    </CardContent>
                  </Card>

                  {/* Skills */}
                  <Card className="card-enhanced">
                    <CardHeader>
                      <CardTitle>Skills & Expertise</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {talent.skills.map((skill) => (
                          <Badge key={skill} variant="secondary" className="theme-glow">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Stats */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Card className="card-enhanced text-center">
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-primary mb-1">{talent.projects}</div>
                        <div className="text-sm text-muted-foreground">Projects Completed</div>
                      </CardContent>
                    </Card>
                    <Card className="card-enhanced text-center">
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-primary mb-1">{talent.successRate}%</div>
                        <div className="text-sm text-muted-foreground">Success Rate</div>
                      </CardContent>
                    </Card>
                    <Card className="card-enhanced text-center">
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-primary mb-1">{talent.reviews}</div>
                        <div className="text-sm text-muted-foreground">Client Reviews</div>
                      </CardContent>
                    </Card>
                    <Card className="card-enhanced text-center">
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-primary mb-1">{talent.responseTime}</div>
                        <div className="text-sm text-muted-foreground">Response Time</div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="portfolio" className="space-y-6">
                  <div className="grid gap-6">
                    {mockPortfolio.map((project) => (
                      <Card key={project.id} className="card-enhanced">
                        <CardContent className="p-6">
                          <div className="flex space-x-4">
                            <div className="w-32 h-24 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                              <Code className="w-8 h-8 text-primary" />
                            </div>
                            <div className="flex-1">
                              <h3 className="text-lg font-bold mb-2">{project.title}</h3>
                              <p className="text-muted-foreground mb-3">{project.description}</p>
                              <div className="flex flex-wrap gap-2 mb-3">
                                {project.technologies.map((tech) => (
                                  <Badge key={tech} variant="outline">{tech}</Badge>
                                ))}
                              </div>
                              <Button variant="outline" size="sm">
                                <ExternalLink className="w-4 h-4 mr-2" />
                                View Project
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="experience" className="space-y-6">
                  {/* Work History */}
                  <Card className="card-enhanced">
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Briefcase className="w-5 h-5" />
                        <span>Work Experience</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {mockWorkHistory.map((job, index) => (
                        <div key={index} className="flex space-x-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                            <Building className="w-6 h-6 text-primary" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-bold">{job.position}</h4>
                            <p className="text-primary font-medium">{job.company}</p>
                            <p className="text-sm text-muted-foreground mb-2">{job.duration}</p>
                            <p className="text-muted-foreground">{job.description}</p>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>

                  {/* Education */}
                  <Card className="card-enhanced">
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <GraduationCap className="w-5 h-5" />
                        <span>Education</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {mockEducation.map((edu, index) => (
                        <div key={index} className="flex space-x-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                            <GraduationCap className="w-6 h-6 text-primary" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-bold">{edu.degree}</h4>
                            <p className="text-primary font-medium">{edu.institution}</p>
                            <p className="text-sm text-muted-foreground">{edu.year} • GPA: {edu.gpa}</p>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="reviews" className="space-y-6">
                  {mockReviews.map((review) => (
                    <Card key={review.id} className="card-enhanced">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h4 className="font-bold">{review.client}</h4>
                            <p className="text-sm text-muted-foreground">{review.company}</p>
                          </div>
                          <div className="flex items-center space-x-1">
                            {[...Array(5)].map((_, i) => (
                              <Star 
                                key={i} 
                                className={`w-4 h-4 ${
                                  i < review.rating 
                                    ? 'fill-yellow-400 text-yellow-400' 
                                    : 'text-gray-300'
                                }`} 
                              />
                            ))}
                          </div>
                        </div>
                        <p className="text-muted-foreground mb-3">{review.comment}</p>
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <span>Project: {review.project}</span>
                          <span>{review.date}</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </TabsContent>

                <TabsContent value="contact" className="space-y-6">
                  <Card className="card-enhanced">
                    <CardHeader>
                      <CardTitle>Contact Information</CardTitle>
                      <CardDescription>
                        Get in touch with {talent.name} for your project
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid gap-4">
                        <Button className="button-premium w-full">
                          <MessageCircle className="w-4 h-4 mr-2" />
                          Send Direct Message
                        </Button>
                        <Button variant="outline" className="w-full">
                          <Calendar className="w-4 h-4 mr-2" />
                          Schedule a Call
                        </Button>
                        <Button variant="outline" className="w-full">
                          <Briefcase className="w-4 h-4 mr-2" />
                          Invite to Project
                        </Button>
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-3">
                        <div className="flex items-center space-x-3 text-sm">
                          <Clock className="w-4 h-4 text-muted-foreground" />
                          <span>Timezone: {talent.timezone || 'PST (UTC-8)'}</span>
                        </div>
                        <div className="flex items-center space-x-3 text-sm">
                          <Globe className="w-4 h-4 text-muted-foreground" />
                          <span>Languages: English (Native), Spanish (Fluent)</span>
                        </div>
                        <div className="flex items-center space-x-3 text-sm">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>Verified Profile</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
