'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight,
  MoreHorizontal
} from 'lucide-react'

interface TalentPaginationProps {
  currentPage: number
  totalPages: number
  totalItems: number
  itemsPerPage: number
  onPageChange: (page: number) => void
  onItemsPerPageChange: (itemsPerPage: number) => void
}

export function TalentPagination({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  onItemsPerPageChange
}: TalentPaginationProps) {
  const startItem = (currentPage - 1) * itemsPerPage + 1
  const endItem = Math.min(currentPage * itemsPerPage, totalItems)

  const getVisiblePages = () => {
    const delta = 2
    const range = []
    const rangeWithDots = []

    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i)
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...')
    } else {
      rangeWithDots.push(1)
    }

    rangeWithDots.push(...range)

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages)
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages)
    }

    return rangeWithDots
  }

  const visiblePages = getVisiblePages()

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 py-6">
      {/* Results Info */}
      <div className="flex items-center space-x-4">
        <p className="text-sm text-muted-foreground">
          Showing {startItem}-{endItem} of {totalItems} talented professionals
        </p>
        
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">Show:</span>
          <Select value={itemsPerPage.toString()} onValueChange={(value) => onItemsPerPageChange(Number(value))}>
            <SelectTrigger className="w-20 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="12">12</SelectItem>
              <SelectItem value="24">24</SelectItem>
              <SelectItem value="48">48</SelectItem>
              <SelectItem value="96">96</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center space-x-2">
        {/* First Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1}
          className="h-8 w-8 p-0"
        >
          <ChevronsLeft className="w-4 h-4" />
        </Button>

        {/* Previous Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="h-8 w-8 p-0"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>

        {/* Page Numbers */}
        <div className="flex items-center space-x-1">
          {visiblePages.map((page, index) => {
            if (page === '...') {
              return (
                <div key={`dots-${index}`} className="flex items-center justify-center w-8 h-8">
                  <MoreHorizontal className="w-4 h-4 text-muted-foreground" />
                </div>
              )
            }

            const pageNumber = page as number
            const isActive = pageNumber === currentPage

            return (
              <motion.div
                key={pageNumber}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant={isActive ? "default" : "outline"}
                  size="sm"
                  onClick={() => onPageChange(pageNumber)}
                  className={`h-8 w-8 p-0 ${
                    isActive 
                      ? 'button-premium' 
                      : 'hover:border-primary/50 hover:text-primary'
                  }`}
                >
                  {pageNumber}
                </Button>
              </motion.div>
            )
          })}
        </div>

        {/* Next Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="h-8 w-8 p-0"
        >
          <ChevronRight className="w-4 h-4" />
        </Button>

        {/* Last Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(totalPages)}
          disabled={currentPage === totalPages}
          className="h-8 w-8 p-0"
        >
          <ChevronsRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )
}

// Advanced Sorting Component
interface TalentSortingProps {
  sortBy: string
  sortOrder: 'asc' | 'desc'
  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void
}

export function TalentSorting({ sortBy, sortOrder, onSortChange }: TalentSortingProps) {
  const sortOptions = [
    { value: 'relevance', label: 'Most Relevant', icon: '🎯' },
    { value: 'rating', label: 'Highest Rated', icon: '⭐' },
    { value: 'experience', label: 'Most Experienced', icon: '🏆' },
    { value: 'rate-low', label: 'Most Affordable', icon: '💰' },
    { value: 'rate-high', label: 'Premium Talent', icon: '💎' },
    { value: 'recent', label: 'Recently Active', icon: '🕒' },
    { value: 'projects', label: 'Most Projects', icon: '📊' },
    { value: 'success-rate', label: 'Highest Success Rate', icon: '✅' },
    { value: 'response-time', label: 'Fastest Response', icon: '⚡' },
    { value: 'availability', label: 'Available Now', icon: '🟢' },
    { value: 'reviews', label: 'Most Reviewed', icon: '💬' },
    { value: 'alphabetical', label: 'A-Z', icon: '🔤' }
  ]

  return (
    <div className="flex items-center space-x-2">
      <span className="text-sm text-muted-foreground">Sort by:</span>
      <Select 
        value={sortBy} 
        onValueChange={(value) => onSortChange(value, sortOrder)}
      >
        <SelectTrigger className="w-48 input-enhanced">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {sortOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              <div className="flex items-center space-x-2">
                <span>{option.icon}</span>
                <span>{option.label}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      <Button
        variant="outline"
        size="sm"
        onClick={() => onSortChange(sortBy, sortOrder === 'asc' ? 'desc' : 'asc')}
        className="h-9 px-3"
      >
        {sortOrder === 'asc' ? '↑' : '↓'}
      </Button>
    </div>
  )
}

// Results Summary Component
interface TalentResultsSummaryProps {
  totalResults: number
  searchQuery: string
  activeFilters: number
  viewMode: 'grid' | 'list'
  onViewModeChange: (mode: 'grid' | 'list') => void
}

export function TalentResultsSummary({
  totalResults,
  searchQuery,
  activeFilters,
  viewMode,
  onViewModeChange
}: TalentResultsSummaryProps) {
  return (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center space-x-4">
        <h2 className="text-lg font-semibold">
          {totalResults.toLocaleString()} Professionals Found
        </h2>
        
        {searchQuery && (
          <div className="text-sm text-muted-foreground">
            for "<span className="font-medium text-foreground">{searchQuery}</span>"
          </div>
        )}
        
        {activeFilters > 0 && (
          <div className="text-sm text-muted-foreground">
            with {activeFilters} filter{activeFilters !== 1 ? 's' : ''} applied
          </div>
        )}
      </div>

      {/* View Mode Toggle */}
      <div className="flex items-center space-x-2 bg-muted/50 rounded-lg p-1">
        <Button
          variant={viewMode === 'grid' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => onViewModeChange('grid')}
          className="h-8 w-8 p-0"
        >
          <div className="grid grid-cols-2 gap-0.5 w-3 h-3">
            <div className="bg-current rounded-[1px]" />
            <div className="bg-current rounded-[1px]" />
            <div className="bg-current rounded-[1px]" />
            <div className="bg-current rounded-[1px]" />
          </div>
        </Button>
        <Button
          variant={viewMode === 'list' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => onViewModeChange('list')}
          className="h-8 w-8 p-0"
        >
          <div className="space-y-0.5 w-3 h-3">
            <div className="bg-current h-0.5 rounded-full" />
            <div className="bg-current h-0.5 rounded-full" />
            <div className="bg-current h-0.5 rounded-full" />
          </div>
        </Button>
      </div>
    </div>
  )
}
