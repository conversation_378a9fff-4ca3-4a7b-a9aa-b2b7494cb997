"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Pa<PERSON>, <PERSON>, <PERSON>, Leaf } from "lucide-react"

type Theme = "blue" | "green" | "dark"

export function ThemeToggle() {
  const [theme, setTheme] = useState<Theme>("blue")

  useEffect(() => {
    const savedTheme = (localStorage.getItem("theme") as Theme) || "blue"
    setTheme(savedTheme)
    applyTheme(savedTheme)
  }, [])

  const applyTheme = (newTheme: Theme) => {
    const root = document.documentElement

    // Remove existing theme classes
    root.classList.remove("theme-blue", "theme-green", "theme-dark")

    // Add new theme class
    root.classList.add(`theme-${newTheme}`)

    // Apply comprehensive theme variables
    switch (newTheme) {
      case "blue":
        // Premium Deep Ocean Blue Theme
        root.style.setProperty("--background", "220 30% 6%")
        root.style.setProperty("--foreground", "210 20% 88%")
        root.style.setProperty("--card", "220 35% 8%")
        root.style.setProperty("--card-foreground", "210 20% 88%")
        root.style.setProperty("--popover", "220 35% 8%")
        root.style.setProperty("--popover-foreground", "210 20% 88%")
        root.style.setProperty("--primary", "217 91% 65%")
        root.style.setProperty("--primary-foreground", "220 30% 6%")
        root.style.setProperty("--secondary", "220 25% 12%")
        root.style.setProperty("--secondary-foreground", "210 20% 88%")
        root.style.setProperty("--muted", "220 25% 12%")
        root.style.setProperty("--muted-foreground", "210 15% 68%")
        root.style.setProperty("--accent", "220 30% 15%")
        root.style.setProperty("--accent-foreground", "217 91% 65%")
        root.style.setProperty("--border", "220 30% 18%")
        root.style.setProperty("--input", "220 30% 15%")
        root.style.setProperty("--ring", "217 91% 65%")
        // Dashboard variables
        root.style.setProperty("--dashboard-sidebar", "220 35% 8%")
        root.style.setProperty("--dashboard-sidebar-foreground", "210 20% 88%")
        root.style.setProperty("--dashboard-topbar", "220 30% 6%")
        root.style.setProperty("--dashboard-topbar-foreground", "210 20% 88%")
        root.style.setProperty("--dashboard-nav-active", "217 91% 65%")
        root.style.setProperty("--dashboard-nav-active-foreground", "220 30% 6%")
        break
      case "green":
        // Rich Dark Forest Green Theme
        root.style.setProperty("--background", "150 25% 8%")
        root.style.setProperty("--foreground", "120 15% 85%")
        root.style.setProperty("--card", "150 30% 10%")
        root.style.setProperty("--card-foreground", "120 15% 85%")
        root.style.setProperty("--popover", "150 30% 10%")
        root.style.setProperty("--popover-foreground", "120 15% 85%")
        root.style.setProperty("--primary", "142 65% 55%")
        root.style.setProperty("--primary-foreground", "150 25% 8%")
        root.style.setProperty("--secondary", "150 20% 15%")
        root.style.setProperty("--secondary-foreground", "120 15% 85%")
        root.style.setProperty("--muted", "150 20% 15%")
        root.style.setProperty("--muted-foreground", "120 10% 65%")
        root.style.setProperty("--accent", "150 25% 18%")
        root.style.setProperty("--accent-foreground", "142 65% 55%")
        root.style.setProperty("--border", "150 25% 20%")
        root.style.setProperty("--input", "150 25% 18%")
        root.style.setProperty("--ring", "142 65% 55%")
        // Dashboard variables
        root.style.setProperty("--dashboard-sidebar", "150 25% 10%")
        root.style.setProperty("--dashboard-sidebar-foreground", "120 15% 85%")
        root.style.setProperty("--dashboard-topbar", "150 25% 8%")
        root.style.setProperty("--dashboard-topbar-foreground", "120 15% 85%")
        root.style.setProperty("--dashboard-nav-active", "142 65% 55%")
        root.style.setProperty("--dashboard-nav-active-foreground", "150 25% 8%")
        break
      case "dark":
        // Sophisticated Dark Theme
        root.style.setProperty("--background", "224 71% 4%")
        root.style.setProperty("--foreground", "210 40% 98%")
        root.style.setProperty("--card", "224 71% 6%")
        root.style.setProperty("--card-foreground", "210 40% 98%")
        root.style.setProperty("--popover", "224 71% 6%")
        root.style.setProperty("--popover-foreground", "210 40% 98%")
        root.style.setProperty("--primary", "217 91% 60%")
        root.style.setProperty("--primary-foreground", "224 71% 4%")
        root.style.setProperty("--secondary", "215 28% 17%")
        root.style.setProperty("--secondary-foreground", "210 40% 98%")
        root.style.setProperty("--muted", "215 28% 17%")
        root.style.setProperty("--muted-foreground", "217 11% 65%")
        root.style.setProperty("--accent", "215 28% 17%")
        root.style.setProperty("--accent-foreground", "210 40% 98%")
        root.style.setProperty("--border", "215 28% 17%")
        root.style.setProperty("--input", "215 28% 17%")
        root.style.setProperty("--ring", "217 91% 60%")
        // Dashboard variables
        root.style.setProperty("--dashboard-sidebar", "224 71% 6%")
        root.style.setProperty("--dashboard-sidebar-foreground", "210 40% 98%")
        root.style.setProperty("--dashboard-topbar", "224 71% 4%")
        root.style.setProperty("--dashboard-topbar-foreground", "210 40% 98%")
        root.style.setProperty("--dashboard-nav-active", "217 91% 60%")
        root.style.setProperty("--dashboard-nav-active-foreground", "224 71% 4%")
        break
    }
  }

  const handleThemeChange = (newTheme: Theme) => {
    setTheme(newTheme)
    localStorage.setItem("theme", newTheme)
    applyTheme(newTheme)
  }

  const themes = [
    { value: "blue" as const, label: "Deep Ocean", icon: Sun, color: "bg-blue-600" },
    { value: "green" as const, label: "Dark Forest", icon: Leaf, color: "bg-green-800" },
    { value: "dark" as const, label: "Midnight", icon: Moon, color: "bg-gray-900" },
  ]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <motion.div whileHover={{ rotate: 180 }} transition={{ duration: 0.3 }}>
            <Palette className="w-5 h-5" />
          </motion.div>
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <AnimatePresence>
          {themes.map((themeOption, index) => (
            <motion.div
              key={themeOption.value}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <DropdownMenuItem
                onClick={() => handleThemeChange(themeOption.value)}
                className="flex items-center space-x-3 cursor-pointer"
              >
                <div className={`w-4 h-4 rounded-full ${themeOption.color}`} />
                <themeOption.icon className="w-4 h-4" />
                <span>{themeOption.label}</span>
                {theme === themeOption.value && (
                  <motion.div layoutId="activeTheme" className="ml-auto w-2 h-2 bg-primary rounded-full" />
                )}
              </DropdownMenuItem>
            </motion.div>
          ))}
        </AnimatePresence>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
