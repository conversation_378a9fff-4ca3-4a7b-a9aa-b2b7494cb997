'use client'

import React from 'react'
import { CheckCircle, XCircle, Clock, AlertCircle, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'

type ActionStatus = 'idle' | 'loading' | 'success' | 'error' | 'warning'

interface ActionIndicatorProps {
  status: ActionStatus
  successMessage?: string
  errorMessage?: string
  loadingMessage?: string
  warningMessage?: string
  className?: string
  showIcon?: boolean
  showMessage?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export function ActionIndicator({
  status,
  successMessage = 'Success!',
  errorMessage = 'Error occurred',
  loadingMessage = 'Processing...',
  warningMessage = 'Warning',
  className,
  showIcon = true,
  showMessage = true,
  size = 'md'
}: ActionIndicatorProps) {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  const getStatusConfig = () => {
    switch (status) {
      case 'loading':
        return {
          icon: <Loader2 className={cn('animate-spin', iconSizes[size])} />,
          message: loadingMessage,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50 border-blue-200'
        }
      case 'success':
        return {
          icon: <CheckCircle className={cn(iconSizes[size])} />,
          message: successMessage,
          color: 'text-green-600',
          bgColor: 'bg-green-50 border-green-200'
        }
      case 'error':
        return {
          icon: <XCircle className={cn(iconSizes[size])} />,
          message: errorMessage,
          color: 'text-red-600',
          bgColor: 'bg-red-50 border-red-200'
        }
      case 'warning':
        return {
          icon: <AlertCircle className={cn(iconSizes[size])} />,
          message: warningMessage,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50 border-yellow-200'
        }
      default:
        return null
    }
  }

  const config = getStatusConfig()

  if (!config || status === 'idle') {
    return null
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className={cn(
          'flex items-center space-x-2 px-3 py-2 rounded-md border',
          config.color,
          config.bgColor,
          sizeClasses[size],
          className
        )}
      >
        {showIcon && config.icon}
        {showMessage && (
          <span className="font-medium">{config.message}</span>
        )}
      </motion.div>
    </AnimatePresence>
  )
}

// Inline status indicator (smaller, no background)
export function InlineStatusIndicator({
  status,
  successMessage = 'Done',
  errorMessage = 'Failed',
  loadingMessage = 'Loading...',
  className,
  size = 'sm'
}: Omit<ActionIndicatorProps, 'showIcon' | 'showMessage' | 'warningMessage'>) {
  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  }

  const getStatusConfig = () => {
    switch (status) {
      case 'loading':
        return {
          icon: <Loader2 className={cn('animate-spin', iconSizes[size])} />,
          message: loadingMessage,
          color: 'text-blue-600'
        }
      case 'success':
        return {
          icon: <CheckCircle className={cn(iconSizes[size])} />,
          message: successMessage,
          color: 'text-green-600'
        }
      case 'error':
        return {
          icon: <XCircle className={cn(iconSizes[size])} />,
          message: errorMessage,
          color: 'text-red-600'
        }
      default:
        return null
    }
  }

  const config = getStatusConfig()

  if (!config || status === 'idle') {
    return null
  }

  return (
    <div className={cn(
      'flex items-center space-x-1 text-xs',
      config.color,
      className
    )}>
      {config.icon}
      <span>{config.message}</span>
    </div>
  )
}

// Toast-style status indicator
export function StatusToast({
  status,
  successMessage = 'Operation completed successfully',
  errorMessage = 'Operation failed',
  loadingMessage = 'Processing your request...',
  onClose,
  autoClose = true,
  duration = 3000
}: ActionIndicatorProps & {
  onClose?: () => void
  autoClose?: boolean
  duration?: number
}) {
  const config = getStatusConfig()

  // Auto close for success/error states
  React.useEffect(() => {
    if (autoClose && (status === 'success' || status === 'error') && onClose) {
      const timer = setTimeout(onClose, duration)
      return () => clearTimeout(timer)
    }
  }, [status, autoClose, duration, onClose])

  if (!config || status === 'idle') {
    return null
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      className={cn(
        'fixed top-4 right-4 z-50 flex items-center space-x-3 px-4 py-3 rounded-lg shadow-lg border',
        config.color,
        config.bgColor,
        'max-w-md'
      )}
    >
      {config.icon}
      <span className="font-medium flex-1">{config.message}</span>
      {onClose && (
        <button
          onClick={onClose}
          className="text-current hover:opacity-70 transition-opacity"
        >
          <XCircle className="w-4 h-4" />
        </button>
      )}
    </motion.div>
  )
}

function getStatusConfig() {
  // This is a placeholder - the actual implementation is in the component
  return null
}
