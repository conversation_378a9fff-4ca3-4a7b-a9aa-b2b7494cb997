'use client'

// Loading System - Comprehensive loading components
// Re-export all loading-related components for easy importing

export {
  LoadingSpinner,
  PulseSpinner,
  DotsSpinner
} from './loading-spinner'

export {
  ButtonLoading,
  ButtonLoadingOverlay,
  SubmitButton,
  SaveButton
} from './button-loading'

export {
  PageLoader,
  InlineLoader,
  SectionLoader,
  FormLoader
} from './page-loader'

export {
  Skeleton,
  JobCardSkeleton,
  ProfileSkeleton,
  TableSkeleton,
  CompanyCardSkeleton,
  SearchResultsSkeleton
} from './skeleton'

export {
  ActionIndicator,
  InlineStatusIndicator,
  StatusToast
} from './action-indicator'

// Loading hooks are exported from a separate file to avoid server component issues
// Import from '@/hooks/use-loading' for loading state management
