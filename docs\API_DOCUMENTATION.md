# Job Portal API Documentation

## Overview

This document describes the REST API endpoints for the Job Portal application. All endpoints follow RESTful conventions and return JSON responses.

## Base URL

```
http://localhost:3000/api/v1
```

## Authentication

Most endpoints require authentication. Include the JW<PERSON> token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All responses follow this format:

```json
{
  "success": true,
  "data": { ... },
  "message": "Optional message",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

Error responses:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "field": "fieldName",
    "details": { ... }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Endpoints

### Authentication

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "role": "job_seeker", // or "company_admin"
  "phone": "+**********",
  "location": {
    "city": "New York",
    "state": "NY",
    "country": "USA"
  }
}
```

#### POST /auth/login
Authenticate user and get tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "rememberMe": false
}
```

### Users

#### GET /users
Get users with pagination (Admin only).

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `role` (string): Filter by user role
- `isActive` (boolean): Filter by active status

#### GET /users/{id}
Get user profile by ID.

#### PUT /users/{id}
Update user profile.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+**********",
  "location": {
    "city": "New York",
    "state": "NY",
    "country": "USA"
  },
  "preferences": {
    "emailNotifications": true,
    "jobAlerts": true,
    "marketingEmails": false,
    "theme": "system"
  }
}
```

#### DELETE /users/{id}
Deactivate user account (Admin only).

### Companies

#### GET /companies
Get companies with pagination and filters.

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `search` (string): Search term
- `industry` (string): Filter by industry
- `size` (string): Filter by company size
- `location` (string): Filter by location
- `isVerified` (boolean): Filter by verification status

#### POST /companies
Create a new company.

**Request Body:**
```json
{
  "name": "Tech Corp Inc.",
  "description": "Leading technology company",
  "website": "https://techcorp.com",
  "industry": "Technology",
  "size": "51-200",
  "location": {
    "address": "123 Tech Street",
    "city": "San Francisco",
    "state": "CA",
    "country": "USA"
  },
  "logo": "https://example.com/logo.png",
  "socialLinks": {
    "linkedin": "https://linkedin.com/company/techcorp",
    "twitter": "https://twitter.com/techcorp"
  }
}
```

#### GET /companies/{id}
Get company by ID or slug.

**Query Parameters:**
- `jobs` (boolean): Include company jobs if true

#### PUT /companies/{id}
Update company profile.

### Jobs

#### GET /jobs
Search and filter jobs.

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `search` (string): Search term
- `location` (string): Location filter
- `remote` (boolean): Remote jobs only
- `type` (string): Job type (full-time, part-time, contract, internship)
- `level` (string): Job level (entry, mid, senior, executive)
- `category` (string): Job category
- `companyId` (string): Filter by company
- `salaryMin` (number): Minimum salary
- `salaryMax` (number): Maximum salary
- `tags` (string): Comma-separated tags

#### POST /jobs
Create a new job posting (Company Admin only).

**Request Body:**
```json
{
  "title": "Senior Software Engineer",
  "description": "We are looking for a senior software engineer...",
  "companyId": "company-id",
  "location": {
    "city": "San Francisco",
    "state": "CA",
    "country": "USA",
    "remote": true
  },
  "salary": {
    "min": 120000,
    "max": 180000,
    "currency": "USD",
    "period": "yearly"
  },
  "requirements": [
    "5+ years of experience",
    "React and Node.js expertise",
    "Strong problem-solving skills"
  ],
  "benefits": [
    "Health insurance",
    "401k matching",
    "Flexible hours"
  ],
  "type": "full-time",
  "level": "senior",
  "category": "Engineering",
  "tags": ["react", "nodejs", "javascript"],
  "applicationDeadline": "2024-12-31T23:59:59.000Z"
}
```

#### GET /jobs/{id}
Get job by ID.

**Query Parameters:**
- `incrementViews` (boolean): Increment view count if true

#### PUT /jobs/{id}
Update job posting (Company Admin only).

#### DELETE /jobs/{id}
Deactivate job posting (Company Admin only).

### Applications

#### GET /applications
Get applications.

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `status` (string): Filter by status
- `jobId` (string): Get applications for specific job (Company view)

#### POST /applications
Apply for a job (Job Seeker only).

**Request Body:**
```json
{
  "jobId": "job-id",
  "coverLetter": "I am interested in this position...",
  "resumeUrl": "https://example.com/resume.pdf",
  "portfolioUrl": "https://example.com/portfolio",
  "expectedSalary": {
    "amount": 150000,
    "currency": "USD",
    "period": "yearly"
  },
  "availableStartDate": "2024-02-01T00:00:00.000Z",
  "customAnswers": [
    {
      "question": "Why do you want to work here?",
      "answer": "Because..."
    }
  ]
}
```

#### GET /applications/{id}
Get application by ID.

#### PUT /applications/{id}
Update application.

**Query Parameters:**
- `action` (string): Action to perform (status, note, withdraw)

**Request Body for status update:**
```json
{
  "status": "reviewing",
  "notes": "Moving to next round"
}
```

### Notifications

#### GET /notifications
Get user notifications.

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `unreadOnly` (boolean): Unread notifications only
- `stats` (boolean): Get notification statistics

#### PUT /notifications
Mark all notifications as read.

**Query Parameters:**
- `action` (string): Must be "markAllRead"

#### GET /notifications/{id}
Get notification by ID.

#### PUT /notifications/{id}
Mark notification as read.

**Query Parameters:**
- `action` (string): Must be "markRead"

#### DELETE /notifications/{id}
Delete notification.

## Error Codes

- `VALIDATION_ERROR`: Request validation failed
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `DUPLICATE_ENTRY`: Resource already exists
- `METHOD_NOT_ALLOWED`: HTTP method not allowed
- `INTERNAL_SERVER_ERROR`: Server error

## Rate Limiting

- Login: 10 requests per 15 minutes per IP
- Registration: 5 requests per 15 minutes per IP
- Other endpoints: Standard rate limiting applies

## Pagination

Paginated responses include pagination metadata:

```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Caching

The API uses in-memory caching for performance:
- Job searches: 5 minutes
- Company data: 10 minutes
- User profiles: 15 minutes
- Notifications: 2 minutes
