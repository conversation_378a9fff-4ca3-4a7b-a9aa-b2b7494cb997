import { NextRequest, NextResponse } from 'next/server'
import { errorService } from '@/lib/errors/error-service'
import { AppError, ErrorCode } from '@/lib/errors/error-types'
import { connectToDatabase } from '@/lib/database/connection'

// Type guard functions
function isError(error: unknown): error is Error {
  return error instanceof Error
}

function hasProperty<T extends string>(
  obj: unknown,
  prop: T
): obj is Record<T, unknown> {
  return typeof obj === 'object' && obj !== null && prop in obj
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    field?: string
    details?: Record<string, any>
  }
  meta?: {
    pagination?: PaginationMeta
    timestamp: string
    requestId: string
    executionTime?: number
    message?: string
  }
}

export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

type RouteHandler<T = any> = (
  request: NextRequest, 
  context?: { params?: Record<string, string> }
) => Promise<NextResponse<ApiResponse<T>>>

export function withErrorHandler<T = any>(
  handler: RouteHandler<T>,
  options: {
    requireAuth?: boolean
    requireDatabase?: boolean
    rateLimit?: {
      requests: number
      windowMs: number
    }
  } = {}
) {
  return async (
    request: NextRequest, 
    context?: { params?: Record<string, string> }
  ): Promise<NextResponse<ApiResponse<T>>> => {
    const startTime = Date.now()
    const requestId = crypto.randomUUID()
    
    try {
      // Add request ID to headers for tracking
      const headers = new Headers()
      headers.set('x-request-id', requestId)
      
      // Connect to database if required
      if (options.requireDatabase !== false) {
        await connectToDatabase()
      }
      
      // Rate limiting (basic implementation)
      if (options.rateLimit) {
        const rateLimitResult = await checkRateLimit(request, options.rateLimit)
        if (!rateLimitResult.allowed) {
          throw errorService.createError(
            ErrorCode.RATE_LIMIT_EXCEEDED,
            `Rate limit exceeded. Try again in ${rateLimitResult.resetTime}ms`,
            undefined,
            { 
              limit: options.rateLimit.requests,
              windowMs: options.rateLimit.windowMs,
              resetTime: rateLimitResult.resetTime
            },
            requestId
          )
        }
      }
      
      // Execute the handler
      const response = await handler(request, context)
      const responseData = await response.json()
      
      // Add execution time and request ID to successful responses
      if (responseData.success && !responseData.meta) {
        responseData.meta = {
          timestamp: new Date().toISOString(),
          requestId,
          executionTime: Date.now() - startTime
        }
      }
      
      // Add headers to response
      const finalResponse = NextResponse.json(responseData, { 
        status: response.status,
        headers
      })
      
      return finalResponse
      
    } catch (error: unknown) {
      let appError: AppError

      if (error instanceof AppError) {
        appError = error
      } else if (isError(error) && (error.name === 'ValidationError' || hasProperty(error, 'code') && (error as any).code === 11000)) {
        appError = errorService.handleDatabaseError(error, requestId)
      } else if (isError(error) && (error.name?.includes('JWT') || error.name?.includes('Token'))) {
        appError = errorService.handleJWTError(error, requestId)
      } else if (hasProperty(error, 'code') && typeof (error as any).code === 'string' && (error as any).code?.startsWith('LIMIT_')) {
        appError = errorService.handleFileUploadError(error as Error, requestId)
      } else {
        const errorMessage = isError(error) ? error.message : 'An unexpected error occurred'
        const errorStack = isError(error) ? error.stack : undefined
        appError = errorService.createError(
          ErrorCode.INTERNAL_SERVER_ERROR,
          process.env.NODE_ENV === 'development'
            ? errorMessage
            : 'An unexpected error occurred',
          undefined,
          process.env.NODE_ENV === 'development'
            ? { originalError: errorMessage, stack: errorStack }
            : undefined,
          requestId
        )
      }
      
      // Log the error
      errorService.logError(appError, request, { 
        handler: handler.name,
        executionTime: Date.now() - startTime
      })
      
      // Create sanitized error response
      const errorResponse = errorService.sanitizeErrorForClient(appError)

      return NextResponse.json(errorResponse, {
        status: appError.statusCode,
        headers: { 'x-request-id': requestId }
      }) as NextResponse<ApiResponse<T>>
    }
  }
}

// Success response helper
export function createSuccessResponse<T>(
  data: T,
  meta?: Partial<ApiResponse['meta']>
): NextResponse<ApiResponse<T>> {
  const response: ApiResponse<T> = {
    success: true,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      requestId: crypto.randomUUID(),
      ...meta
    }
  }
  
  return NextResponse.json(response)
}

// Pagination helper
export function createPaginatedResponse<T>(
  data: T[],
  pagination: {
    page: number
    limit: number
    total: number
  },
  meta?: Partial<ApiResponse['meta']>
): NextResponse<ApiResponse<T[]>> {
  const totalPages = Math.ceil(pagination.total / pagination.limit)
  
  const paginationMeta: PaginationMeta = {
    page: pagination.page,
    limit: pagination.limit,
    total: pagination.total,
    totalPages,
    hasNext: pagination.page < totalPages,
    hasPrev: pagination.page > 1
  }
  
  return createSuccessResponse(data, {
    pagination: paginationMeta,
    ...meta
  })
}

// Basic rate limiting implementation
// In production, you'd want to use Redis or a proper rate limiting service
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

async function checkRateLimit(
  request: NextRequest, 
  config: { requests: number; windowMs: number }
): Promise<{ allowed: boolean; resetTime?: number }> {
  const ip = request.headers.get('x-forwarded-for') || 
            request.headers.get('x-real-ip') || 
            'unknown'
  
  const now = Date.now()
  const key = `${ip}:${Math.floor(now / config.windowMs)}`
  
  const current = rateLimitStore.get(key)
  
  if (!current) {
    rateLimitStore.set(key, { count: 1, resetTime: now + config.windowMs })
    return { allowed: true }
  }
  
  if (current.count >= config.requests) {
    return { 
      allowed: false, 
      resetTime: current.resetTime - now 
    }
  }
  
  current.count++
  return { allowed: true }
}

// Cleanup old rate limit entries periodically
setInterval(() => {
  const now = Date.now()
  for (const [key, value] of rateLimitStore.entries()) {
    if (value.resetTime < now) {
      rateLimitStore.delete(key)
    }
  }
}, 60000) // Cleanup every minute

// Method validation helper
export function validateMethod(request: NextRequest, allowedMethods: string[]): void {
  if (!allowedMethods.includes(request.method)) {
    throw errorService.createError(
      ErrorCode.METHOD_NOT_ALLOWED,
      `Method ${request.method} not allowed. Allowed methods: ${allowedMethods.join(', ')}`
    )
  }
}

// Request body validation helper
export async function validateRequestBody<T>(
  request: NextRequest,
  validator: (data: any) => T
): Promise<T> {
  try {
    const body = await request.json()
    return validator(body)
  } catch (error) {
    if (error instanceof SyntaxError) {
      throw errorService.createError(
        ErrorCode.INVALID_FORMAT,
        'Invalid JSON in request body'
      )
    }
    throw error
  }
}
