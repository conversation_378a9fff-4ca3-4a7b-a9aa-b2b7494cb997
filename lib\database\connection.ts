import mongoose from 'mongoose'
import { GridFSBucket } from 'mongodb'

interface MongoConnection {
  conn: typeof mongoose | null
  promise: Promise<typeof mongoose> | null
  bucket: GridFSBucket | null
}

// Global variable to cache the database connection
declare global {
  var mongo: MongoConnection | undefined
}

const MONGODB_URI = process.env.MONGODB_URI!
const MONGODB_DB_NAME = process.env.MONGODB_DB_NAME || 'jobportal'

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env.local')
}

let cached = global.mongo

if (!cached) {
  cached = global.mongo = { conn: null, promise: null, bucket: null }
}

export async function connectToDatabase(): Promise<typeof mongoose> {
  if (cached!.conn) {
    return cached!.conn
  }

  if (!cached!.promise) {
    const opts = {
      bufferCommands: false,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      family: 4
    }

    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
      console.log('✅ Connected to MongoDB')
      return mongoose
    })
  }

  try {
    cached!.conn = await cached!.promise
  } catch (e) {
    cached!.promise = null
    console.error('❌ MongoDB connection error:', e)
    throw e
  }

  return cached!.conn
}

export async function getGridFSBucket(): Promise<GridFSBucket> {
  if (cached!.bucket) {
    return cached!.bucket
  }

  const mongoose = await connectToDatabase()
  
  if (!mongoose.connection.db) {
    throw new Error('Database connection not established')
  }

  cached!.bucket = new GridFSBucket(mongoose.connection.db, {
    bucketName: 'jobportal_files'
  })

  console.log('✅ GridFS bucket initialized')
  return cached!.bucket
}

export async function disconnectFromDatabase(): Promise<void> {
  if (cached!.conn) {
    await cached!.conn.disconnect()
    cached!.conn = null
    cached!.promise = null
    cached!.bucket = null
    console.log('✅ Disconnected from MongoDB')
  }
}

// Handle connection events
mongoose.connection.on('connected', () => {
  console.log('📡 Mongoose connected to MongoDB')
})

mongoose.connection.on('error', (err) => {
  console.error('❌ Mongoose connection error:', err)
})

mongoose.connection.on('disconnected', () => {
  console.log('📡 Mongoose disconnected from MongoDB')
})

// Graceful shutdown
process.on('SIGINT', async () => {
  await disconnectFromDatabase()
  process.exit(0)
})
