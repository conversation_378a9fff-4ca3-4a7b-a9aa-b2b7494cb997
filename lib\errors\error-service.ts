import { AppError, ErrorCode, ERROR_CONFIGS } from './error-types'
import { NextRequest } from 'next/server'
import mongoose from 'mongoose'

class BackendErrorService {
  private static instance: BackendErrorService
  
  static getInstance(): BackendErrorService {
    if (!BackendErrorService.instance) {
      BackendErrorService.instance = new BackendErrorService()
    }
    return BackendErrorService.instance
  }

  createError(
    code: ErrorCode, 
    message?: string, 
    field?: string, 
    details?: Record<string, any>,
    requestId?: string
  ): AppError {
    const errorConfig = ERROR_CONFIGS[code]
    
    return new AppError({
      code,
      message: message || errorConfig.defaultMessage,
      statusCode: errorConfig.statusCode,
      field,
      details,
      requestId
    })
  }

  handleDatabaseError(error: any, requestId?: string): AppError {
    // MongoDB duplicate key error
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern || {})[0] || 'field'
      const value = error.keyValue?.[field]
      
      return this.createError(
        ErrorCode.DUPLICATE_ENTRY,
        `${field} '${value}' already exists`,
        field,
        { duplicateField: field, duplicateValue: value },
        requestId
      )
    }
    
    // Mongoose validation error
    if (error.name === 'ValidationError') {
      const field = Object.keys(error.errors)[0]
      const validationError = error.errors[field]
      
      return this.createError(
        ErrorCode.VALIDATION_ERROR,
        validationError.message,
        field,
        { validationErrors: error.errors },
        requestId
      )
    }
    
    // Mongoose cast error (invalid ObjectId)
    if (error.name === 'CastError') {
      return this.createError(
        ErrorCode.INVALID_OBJECT_ID,
        `Invalid ${error.path} format`,
        error.path,
        { value: error.value, expectedType: error.kind },
        requestId
      )
    }
    
    // MongoDB connection error
    if (error.name === 'MongoNetworkError' || error.name === 'MongoServerError') {
      return this.createError(
        ErrorCode.DATABASE_CONNECTION_ERROR,
        'Database connection failed',
        undefined,
        { originalError: error.message },
        requestId
      )
    }
    
    // Default database error
    return this.createError(
      ErrorCode.DATABASE_CONNECTION_ERROR,
      error.message || 'Database operation failed',
      undefined,
      { originalError: error.message },
      requestId
    )
  }

  handleFileUploadError(error: any, requestId?: string): AppError {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return this.createError(
        ErrorCode.FILE_TOO_LARGE,
        `File size exceeds limit of ${this.formatFileSize(error.limit)}`,
        'file',
        { limit: error.limit, field: error.field },
        requestId
      )
    }
    
    if (error.code === 'LIMIT_FILE_COUNT') {
      return this.createError(
        ErrorCode.VALIDATION_ERROR,
        `Too many files. Maximum ${error.limit} files allowed`,
        'files',
        { limit: error.limit },
        requestId
      )
    }
    
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return this.createError(
        ErrorCode.VALIDATION_ERROR,
        `Unexpected file field: ${error.field}`,
        error.field,
        { field: error.field },
        requestId
      )
    }
    
    if (error.message?.includes('Invalid file type')) {
      return this.createError(
        ErrorCode.INVALID_FILE_TYPE,
        error.message,
        'file',
        undefined,
        requestId
      )
    }
    
    return this.createError(
      ErrorCode.UPLOAD_FAILED,
      error.message || 'File upload failed',
      'file',
      { originalError: error.message },
      requestId
    )
  }

  handleJWTError(error: any, requestId?: string): AppError {
    if (error.name === 'TokenExpiredError') {
      return this.createError(
        ErrorCode.TOKEN_EXPIRED,
        'Token has expired',
        'token',
        { expiredAt: error.expiredAt },
        requestId
      )
    }
    
    if (error.name === 'JsonWebTokenError') {
      return this.createError(
        ErrorCode.TOKEN_INVALID,
        'Invalid token',
        'token',
        { originalError: error.message },
        requestId
      )
    }
    
    if (error.name === 'NotBeforeError') {
      return this.createError(
        ErrorCode.TOKEN_INVALID,
        'Token not active yet',
        'token',
        { notBefore: error.notBefore },
        requestId
      )
    }
    
    return this.createError(
      ErrorCode.TOKEN_INVALID,
      'Token validation failed',
      'token',
      { originalError: error.message },
      requestId
    )
  }

  logError(error: AppError, request?: NextRequest, context?: Record<string, any>): void {
    const logData = {
      error: {
        code: error.code,
        message: error.message,
        statusCode: error.statusCode,
        field: error.field,
        details: error.details,
        timestamp: error.timestamp,
        requestId: error.requestId,
        stack: error.stack
      },
      request: request ? {
        url: request.url,
        method: request.method,
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
        referer: request.headers.get('referer')
      } : undefined,
      context,
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    }
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('🚨 API Error:', JSON.stringify(logData, null, 2))
    } else {
      // In production, log as JSON for structured logging
      console.error(JSON.stringify(logData))
      
      // Send to external logging service if configured
      this.sendToLoggingService(logData)
    }
  }

  private sendToLoggingService(logData: any): void {
    // Implementation for external logging service
    // Examples:
    // - Sentry: Sentry.captureException(logData)
    // - LogRocket: LogRocket.captureException(logData)
    // - DataDog: logger.error(logData)
    // - Custom webhook: fetch('/api/logs', { method: 'POST', body: JSON.stringify(logData) })
    
    // For now, we'll just ensure it's logged to console
    if (process.env.LOGGING_WEBHOOK_URL) {
      fetch(process.env.LOGGING_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(logData)
      }).catch(err => {
        console.error('Failed to send log to external service:', err)
      })
    }
  }

  private formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  // Utility method to check if error is operational (expected) or programming error
  isOperationalError(error: Error): boolean {
    if (error instanceof AppError) {
      return error.isOperational
    }
    
    // Known operational errors
    const operationalErrors = [
      'ValidationError',
      'CastError',
      'MongoNetworkError',
      'TokenExpiredError',
      'JsonWebTokenError'
    ]
    
    return operationalErrors.includes(error.name)
  }

  // Method to sanitize error for client response
  sanitizeErrorForClient(error: AppError): object {
    return {
      success: false,
      error: {
        code: error.code,
        message: error.message,
        field: error.field,
        // Only include details in development or for certain error types
        ...(process.env.NODE_ENV === 'development' || this.shouldIncludeDetails(error.code) 
          ? { details: error.details } 
          : {}
        )
      },
      meta: {
        timestamp: error.timestamp.toISOString(),
        requestId: error.requestId
      }
    }
  }

  private shouldIncludeDetails(code: ErrorCode): boolean {
    // Error codes where it's safe to include details in production
    const safeDetailCodes = [
      ErrorCode.VALIDATION_ERROR,
      ErrorCode.REQUIRED_FIELD_MISSING,
      ErrorCode.INVALID_FORMAT,
      ErrorCode.DUPLICATE_ENTRY,
      ErrorCode.QUOTA_EXCEEDED
    ]
    
    return safeDetailCodes.includes(code)
  }
}

export const errorService = BackendErrorService.getInstance()
