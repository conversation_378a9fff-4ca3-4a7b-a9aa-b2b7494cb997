// Comprehensive job data service
export interface Job {
  id: number
  title: string
  company: {
    id: number
    name: string
    logo: string
    industry: string
    size: string
    rating: number
    verified: boolean
  }
  location: string
  locationDetails?: {
    city: string
    region: string
    country: string
    continent: string
    coordinates?: {
      latitude: number
      longitude: number
    }
  }
  type: 'Full-time' | 'Part-time' | 'Contract' | 'Freelance' | 'Internship'
  remote: boolean
  salary: {
    min: number
    max: number
    currency: string
    period: 'hour' | 'month' | 'year'
  }
  experience: 'Entry Level' | 'Mid Level' | 'Senior Level' | 'Executive'
  description: string
  requirements: string[]
  responsibilities: string[]
  benefits: string[]
  skills: string[]
  department: string
  posted: string
  deadline?: string
  applicants: number
  views: number
  featured: boolean
  urgent: boolean
  category: string
  workModel: 'On-site' | 'Remote' | 'Hybrid'
  applicationStatus: 'open' | 'closing-soon' | 'closed' | 'filled'
  maxApplicants?: number
  closingReason?: string
  education?: string
  languages?: string[]
  companyDescription?: string
  teamSize?: string
  reportingTo?: string
  travelRequired?: string
  securityClearance?: boolean
  visaSponsorship?: boolean
}

export interface JobApplication {
  id: number
  jobId: number
  applicantName: string
  email: string
  phone: string
  resume: string
  coverLetter: string
  status: 'Applied' | 'Reviewing' | 'Interview' | 'Offer' | 'Rejected'
  appliedDate: string
}

// Mock job data with comprehensive details
export const jobs: Job[] = [
  {
    id: 1,
    title: "Senior Software Engineer",
    company: {
      id: 1,
      name: "TechCorp Inc.",
      logo: "/api/placeholder/60/60",
      industry: "Software Development",
      size: "500-1000",
      rating: 4.8,
      verified: true
    },
    location: "San Francisco, CA",
    locationDetails: {
      city: "San Francisco",
      region: "California",
      country: "United States",
      continent: "North America",
      coordinates: {
        latitude: 37.7749,
        longitude: -122.4194
      }
    },
    type: "Full-time",
    remote: false,
    salary: {
      min: 120000,
      max: 180000,
      currency: "USD",
      period: "year"
    },
    experience: "Senior Level",
    description: "Join our core platform team to build scalable microservices that power our enterprise customers. You'll work on high-impact projects using cutting-edge technologies and collaborate with a world-class engineering team to solve complex technical challenges.",
    requirements: [
      "5+ years of software engineering experience",
      "Strong proficiency in React, Node.js, and TypeScript",
      "Experience with AWS cloud services and microservices architecture",
      "Knowledge of system design and distributed systems",
      "Experience with CI/CD pipelines and DevOps practices",
      "Strong problem-solving and communication skills"
    ],
    responsibilities: [
      "Design and develop scalable backend services and APIs",
      "Collaborate with product managers and designers on feature development",
      "Mentor junior engineers and conduct code reviews",
      "Participate in system architecture decisions and technical planning",
      "Optimize application performance and ensure high availability",
      "Contribute to engineering best practices and team culture"
    ],
    benefits: [
      "Competitive salary with equity compensation",
      "Comprehensive health, dental, and vision insurance",
      "Unlimited PTO and flexible working hours",
      "$3,000 annual learning and development budget",
      "Top-tier equipment and home office setup allowance",
      "Catered meals and snacks in the office"
    ],
    skills: ["React", "Node.js", "TypeScript", "AWS", "Microservices", "System Design", "PostgreSQL", "Redis"],
    department: "Engineering",
    posted: "2 days ago",
    deadline: "2024-02-15",
    applicants: 47,
    views: 1250,
    featured: true,
    urgent: false,
    category: "Software Development",
    workModel: "Hybrid",
    education: "Bachelor's degree in Computer Science or equivalent experience",
    languages: ["English"],
    teamSize: "8-12 engineers",
    reportingTo: "Engineering Manager",
    travelRequired: "Minimal (< 10%)",
    visaSponsorship: true,
    applicationStatus: "open",
    maxApplicants: 100
  },
  {
    id: 2,
    title: "Product Manager",
    company: {
      id: 1,
      name: "TechCorp Inc.",
      logo: "/api/placeholder/60/60",
      industry: "Software Development",
      size: "500-1000",
      rating: 4.8,
      verified: true
    },
    location: "Remote",
    type: "Full-time",
    remote: true,
    salary: {
      min: 130000,
      max: 160000,
      currency: "USD",
      period: "year"
    },
    experience: "Mid Level",
    description: "Lead product strategy and roadmap for our AI-powered analytics platform. Drive product vision from conception to launch, working closely with engineering, design, and data science teams to deliver exceptional user experiences.",
    requirements: [
      "3+ years of product management experience in B2B SaaS",
      "Strong analytical skills and experience with data-driven decision making",
      "Knowledge of AI/ML concepts and analytics platforms",
      "Experience with agile development methodologies",
      "Excellent communication and stakeholder management skills",
      "Technical background or ability to work closely with engineering teams"
    ],
    responsibilities: [
      "Define and execute product strategy and roadmap",
      "Conduct user research and gather customer feedback",
      "Work with engineering teams to prioritize features and manage releases",
      "Analyze product metrics and user behavior to drive improvements",
      "Collaborate with sales and marketing on go-to-market strategies",
      "Present product updates to leadership and stakeholders"
    ],
    benefits: [
      "Competitive salary with performance bonuses",
      "Comprehensive health and wellness benefits",
      "Remote-first culture with flexible hours",
      "Professional development opportunities and conference attendance",
      "Stock options with high growth potential",
      "Home office setup and co-working space allowance"
    ],
    skills: ["Product Strategy", "Analytics", "A/B Testing", "User Research", "Agile", "SQL", "Figma", "Jira"],
    department: "Product",
    posted: "1 week ago",
    deadline: "2024-02-20",
    applicants: 32,
    views: 890,
    featured: false,
    urgent: false,
    category: "Product Management",
    workModel: "Remote",
    education: "Bachelor's degree in Business, Engineering, or related field",
    languages: ["English"],
    teamSize: "Cross-functional team of 15+",
    reportingTo: "VP of Product",
    travelRequired: "Quarterly team meetings",
    visaSponsorship: false,
    applicationStatus: "closing-soon",
    maxApplicants: 50
  },
  {
    id: 3,
    title: "Senior UX Designer",
    company: {
      id: 2,
      name: "DesignStudio Pro",
      logo: "/api/placeholder/60/60",
      industry: "Design & Creative",
      size: "51-200",
      rating: 4.9,
      verified: true
    },
    location: "Cape Town, South Africa",
    locationDetails: {
      city: "Cape Town",
      region: "Western Cape",
      country: "South Africa",
      continent: "Africa",
      coordinates: {
        latitude: -33.9249,
        longitude: 18.4241
      }
    },
    type: "Full-time",
    remote: false,
    salary: {
      min: 95000,
      max: 130000,
      currency: "USD",
      period: "year"
    },
    experience: "Senior Level",
    description: "Lead UX design for major client projects including Fortune 500 brands. Drive design strategy, create user-centered experiences, and mentor junior designers while working on cutting-edge digital products.",
    requirements: [
      "5+ years of UX design experience with complex projects",
      "Strong portfolio demonstrating design process and outcomes",
      "Expertise in design systems and component libraries",
      "Experience with client management and stakeholder communication",
      "Proficiency in Figma, Sketch, and prototyping tools",
      "Knowledge of front-end development principles"
    ],
    responsibilities: [
      "Lead UX design for high-profile client projects",
      "Conduct user research and usability testing",
      "Create wireframes, prototypes, and design specifications",
      "Collaborate with developers to ensure design implementation",
      "Mentor junior designers and provide design feedback",
      "Present design concepts to clients and stakeholders"
    ],
    benefits: [
      "Competitive salary with creative bonuses",
      "Premium design tools and software licenses",
      "Flexible work arrangements and creative time",
      "Professional development and design conference attendance",
      "Health insurance and wellness programs",
      "Inspiring office space in Manhattan"
    ],
    skills: ["UX Design", "Figma", "User Research", "Prototyping", "Design Systems", "Usability Testing", "Sketch", "Adobe Creative Suite"],
    department: "Design",
    posted: "1 day ago",
    deadline: "2024-02-10",
    applicants: 28,
    views: 650,
    featured: true,
    urgent: true,
    category: "Design",
    workModel: "On-site",
    education: "Bachelor's degree in Design, HCI, or related field",
    languages: ["English"],
    teamSize: "6-8 designers",
    reportingTo: "Creative Director",
    travelRequired: "Client visits as needed",
    visaSponsorship: false,
    applicationStatus: "open",
    maxApplicants: 75
  },
  {
    id: 4,
    title: "Data Scientist",
    company: {
      id: 3,
      name: "HealthTech Solutions",
      logo: "/api/placeholder/60/60",
      industry: "Healthcare Technology",
      size: "1000+",
      rating: 4.7,
      verified: true
    },
    location: "Boston, MA",
    type: "Full-time",
    remote: false,
    salary: {
      min: 130000,
      max: 170000,
      currency: "USD",
      period: "year"
    },
    experience: "Senior Level",
    description: "Apply machine learning to healthcare data to improve patient outcomes and clinical decision-making. Work with large-scale medical datasets to develop predictive models and analytics solutions.",
    requirements: [
      "PhD in Computer Science, Statistics, or related field",
      "5+ years of experience with healthcare data and medical research",
      "Strong proficiency in Python, R, and machine learning frameworks",
      "Experience with clinical data standards (FHIR, HL7)",
      "Knowledge of healthcare regulations and privacy requirements",
      "Published research in healthcare AI or related fields"
    ],
    responsibilities: [
      "Develop machine learning models for clinical applications",
      "Analyze large-scale healthcare datasets for insights",
      "Collaborate with clinical teams on research projects",
      "Ensure compliance with healthcare data privacy regulations",
      "Present findings to medical professionals and stakeholders",
      "Contribute to peer-reviewed publications and conferences"
    ],
    benefits: [
      "Competitive salary with research bonuses",
      "Comprehensive health benefits including mental health support",
      "Flexible work arrangements and sabbatical opportunities",
      "Conference attendance and continuing education support",
      "Access to cutting-edge healthcare data and research",
      "Opportunity to make direct impact on patient care"
    ],
    skills: ["Python", "Machine Learning", "TensorFlow", "Healthcare Data", "Clinical Research", "Statistics", "R", "SQL"],
    department: "Data Science",
    posted: "3 days ago",
    deadline: "2024-02-25",
    applicants: 19,
    views: 420,
    featured: false,
    urgent: false,
    category: "Data Science",
    workModel: "Hybrid",
    education: "PhD in relevant field required",
    languages: ["English"],
    teamSize: "12-15 data scientists",
    reportingTo: "Head of Data Science",
    travelRequired: "Conference travel",
    securityClearance: true,
    visaSponsorship: true,
    applicationStatus: "closed",
    maxApplicants: 30,
    closingReason: "Position filled - we found an exceptional candidate"
  },
  {
    id: 5,
    title: "Frontend Developer",
    company: {
      id: 1,
      name: "TechCorp Inc.",
      logo: "/api/placeholder/60/60",
      industry: "Software Development",
      size: "500-1000",
      rating: 4.8,
      verified: true
    },
    location: "Austin, TX",
    type: "Full-time",
    remote: true,
    salary: {
      min: 90000,
      max: 130000,
      currency: "USD",
      period: "year"
    },
    experience: "Mid Level",
    description: "Join our frontend team to build beautiful, responsive user interfaces using React and modern web technologies. You'll work closely with designers and backend engineers to create exceptional user experiences.",
    requirements: [
      "3+ years of frontend development experience",
      "Strong proficiency in React, TypeScript, and modern JavaScript",
      "Experience with CSS frameworks and responsive design",
      "Knowledge of state management (Redux, Zustand, or similar)",
      "Familiarity with testing frameworks (Jest, React Testing Library)",
      "Understanding of web performance optimization"
    ],
    responsibilities: [
      "Develop and maintain responsive web applications using React",
      "Collaborate with UX/UI designers to implement pixel-perfect designs",
      "Optimize applications for maximum speed and scalability",
      "Write clean, maintainable, and well-documented code",
      "Participate in code reviews and mentor junior developers",
      "Stay up-to-date with the latest frontend technologies and best practices"
    ],
    benefits: [
      "Competitive salary with annual bonuses",
      "Remote-first work environment",
      "Health, dental, and vision insurance",
      "Professional development budget",
      "Latest MacBook Pro and equipment",
      "Flexible working hours"
    ],
    skills: ["React", "TypeScript", "JavaScript", "CSS", "HTML", "Redux", "Next.js", "Tailwind CSS"],
    department: "Engineering",
    posted: "1 day ago",
    deadline: "2024-02-12",
    applicants: 23,
    views: 580,
    featured: false,
    urgent: false,
    category: "Software Development",
    workModel: "Remote",
    education: "Bachelor's degree in Computer Science or equivalent experience",
    languages: ["English"],
    teamSize: "6-8 frontend engineers",
    reportingTo: "Frontend Team Lead",
    travelRequired: "None",
    visaSponsorship: true,
    applicationStatus: "open",
    maxApplicants: 80
  },
  {
    id: 6,
    title: "Marketing Manager",
    company: {
      id: 4,
      name: "FinanceFlow",
      logo: "/api/placeholder/60/60",
      industry: "Financial Technology",
      size: "51-200",
      rating: 4.6,
      verified: false
    },
    location: "Nairobi, Kenya",
    locationDetails: {
      city: "Nairobi",
      region: "Nairobi County",
      country: "Kenya",
      continent: "Africa",
      coordinates: {
        latitude: -1.2921,
        longitude: 36.8219
      }
    },
    type: "Full-time",
    remote: false,
    salary: {
      min: 80000,
      max: 110000,
      currency: "USD",
      period: "year"
    },
    experience: "Mid Level",
    description: "Drive marketing strategy and execution for our fintech platform. Lead digital marketing campaigns, content creation, and growth initiatives to acquire and retain customers in the competitive fintech space.",
    requirements: [
      "3+ years of marketing experience in fintech or financial services",
      "Strong knowledge of digital marketing channels and analytics",
      "Experience with marketing automation and CRM platforms",
      "Understanding of financial regulations and compliance",
      "Excellent written and verbal communication skills",
      "Data-driven approach to marketing optimization"
    ],
    responsibilities: [
      "Develop and execute comprehensive marketing strategies",
      "Manage digital advertising campaigns across multiple channels",
      "Create compelling content for various marketing materials",
      "Analyze marketing performance and optimize campaigns",
      "Collaborate with product and sales teams on go-to-market strategies",
      "Manage marketing budget and vendor relationships"
    ],
    benefits: [
      "Competitive salary with performance incentives",
      "Health insurance and wellness benefits",
      "Flexible work hours and remote work options",
      "Professional development and marketing conference attendance",
      "Stock options in growing fintech company",
      "Modern office space in Austin tech hub"
    ],
    skills: ["Digital Marketing", "SEO/SEM", "Content Marketing", "Analytics", "Marketing Automation", "CRM", "Social Media", "A/B Testing"],
    department: "Marketing",
    posted: "5 days ago",
    deadline: "2024-02-18",
    applicants: 41,
    views: 780,
    featured: false,
    urgent: false,
    category: "Marketing",
    workModel: "Hybrid",
    education: "Bachelor's degree in Marketing, Business, or related field",
    languages: ["English", "Spanish (preferred)"],
    teamSize: "5-7 marketing professionals",
    reportingTo: "VP of Marketing",
    travelRequired: "Industry events and conferences",
    visaSponsorship: false
  },
  {
    id: 7,
    title: "DevOps Engineer",
    company: {
      id: 1,
      name: "TechCorp Inc.",
      logo: "/api/placeholder/60/60",
      industry: "Software Development",
      size: "500-1000",
      rating: 4.8,
      verified: true
    },
    location: "San Francisco, CA",
    type: "Full-time",
    remote: false,
    salary: {
      min: 140000,
      max: 190000,
      currency: "USD",
      period: "year"
    },
    experience: "Senior Level",
    description: "Join our platform engineering team to build and maintain scalable infrastructure that powers our enterprise applications. You'll work with cutting-edge cloud technologies and automation tools.",
    requirements: [
      "5+ years of DevOps/Platform engineering experience",
      "Strong experience with AWS, Kubernetes, and Docker",
      "Proficiency in Infrastructure as Code (Terraform, CloudFormation)",
      "Experience with CI/CD pipelines and automation tools",
      "Knowledge of monitoring and observability tools",
      "Strong scripting skills in Python, Bash, or Go"
    ],
    responsibilities: [
      "Design and maintain scalable cloud infrastructure",
      "Implement and optimize CI/CD pipelines",
      "Monitor system performance and ensure high availability",
      "Automate deployment and operational processes",
      "Collaborate with development teams on infrastructure needs",
      "Implement security best practices and compliance requirements"
    ],
    benefits: [
      "Top-tier compensation with equity",
      "Comprehensive health and wellness benefits",
      "Flexible work arrangements",
      "Professional development and certification support",
      "Latest technology and equipment",
      "Catered meals and office perks"
    ],
    skills: ["AWS", "Kubernetes", "Docker", "Terraform", "Python", "CI/CD", "Monitoring", "Linux"],
    department: "Platform Engineering",
    posted: "3 days ago",
    deadline: "2024-02-20",
    applicants: 18,
    views: 420,
    featured: true,
    urgent: false,
    category: "Software Development",
    workModel: "Hybrid",
    education: "Bachelor's degree in Computer Science or equivalent experience",
    languages: ["English"],
    teamSize: "8-10 platform engineers",
    reportingTo: "Platform Engineering Manager",
    travelRequired: "Minimal (< 5%)",
    visaSponsorship: true,
    applicationStatus: "filled",
    maxApplicants: 25,
    closingReason: "Position has been filled - thank you to all applicants"
  },
  {
    id: 8,
    title: "Brand Designer",
    company: {
      id: 2,
      name: "DesignStudio Pro",
      logo: "/api/placeholder/60/60",
      industry: "Design & Creative",
      size: "51-200",
      rating: 4.9,
      verified: true
    },
    location: "Los Angeles, CA",
    type: "Full-time",
    remote: false,
    salary: {
      min: 70000,
      max: 95000,
      currency: "USD",
      period: "year"
    },
    experience: "Mid Level",
    description: "Create compelling brand identities and visual systems for diverse clients. Work across print and digital mediums to bring brands to life with creative excellence and strategic thinking.",
    requirements: [
      "3+ years of brand design experience",
      "Proficiency in Adobe Creative Suite (Illustrator, Photoshop, InDesign)",
      "Strong typography and layout skills",
      "Experience with brand strategy and identity development",
      "Portfolio demonstrating diverse brand work",
      "Understanding of print production processes"
    ],
    responsibilities: [
      "Develop comprehensive brand identity systems",
      "Create visual assets across print and digital platforms",
      "Collaborate with strategy team on brand positioning",
      "Present design concepts to clients and stakeholders",
      "Maintain brand consistency across all touchpoints",
      "Stay current with design trends and industry best practices"
    ],
    benefits: [
      "Competitive salary with creative bonuses",
      "Premium design software and tools",
      "Flexible work schedule",
      "Professional development opportunities",
      "Health insurance and wellness programs",
      "Inspiring creative workspace"
    ],
    skills: ["Brand Design", "Adobe Creative Suite", "Typography", "Logo Design", "Print Design", "Brand Strategy", "Illustration", "Layout Design"],
    department: "Brand",
    posted: "4 days ago",
    deadline: "2024-02-15",
    applicants: 35,
    views: 650,
    featured: false,
    urgent: false,
    category: "Design",
    workModel: "On-site",
    education: "Bachelor's degree in Graphic Design, Visual Arts, or related field",
    languages: ["English"],
    teamSize: "4-6 brand designers",
    reportingTo: "Brand Director",
    travelRequired: "Client visits as needed",
    visaSponsorship: false,
    applicationStatus: "closing-soon",
    maxApplicants: 60
  },
  {
    id: 9,
    title: "Full Stack Engineer",
    company: {
      id: 1,
      name: "TechCorp Inc.",
      logo: "/api/placeholder/60/60",
      industry: "Software Development",
      size: "500-1000",
      rating: 4.8,
      verified: true
    },
    location: "Remote",
    type: "Full-time",
    remote: true,
    salary: {
      min: 110000,
      max: 160000,
      currency: "USD",
      period: "year"
    },
    experience: "Mid Level",
    description: "Build end-to-end features for our enterprise platform using modern web technologies. Work with React, Node.js, and cloud infrastructure to deliver scalable solutions.",
    requirements: [
      "4+ years of full-stack development experience",
      "Proficiency in React, Node.js, and TypeScript",
      "Experience with databases (PostgreSQL, MongoDB)",
      "Knowledge of cloud platforms (AWS, GCP, or Azure)",
      "Understanding of microservices architecture",
      "Strong problem-solving and communication skills"
    ],
    responsibilities: [
      "Develop full-stack features from conception to deployment",
      "Design and implement RESTful APIs and GraphQL endpoints",
      "Build responsive user interfaces with React",
      "Optimize application performance and scalability",
      "Collaborate with product and design teams",
      "Participate in code reviews and technical discussions"
    ],
    benefits: [
      "Competitive salary with equity package",
      "100% remote work flexibility",
      "Health, dental, and vision insurance",
      "Annual learning and development budget",
      "Home office setup allowance",
      "Flexible PTO policy"
    ],
    skills: ["React", "Node.js", "TypeScript", "PostgreSQL", "AWS", "GraphQL", "Docker", "Git"],
    department: "Engineering",
    posted: "2 days ago",
    deadline: "2024-02-25",
    applicants: 31,
    views: 720,
    featured: false,
    urgent: false,
    category: "Software Development",
    workModel: "Remote",
    education: "Bachelor's degree in Computer Science or equivalent experience",
    languages: ["English"],
    teamSize: "10-15 engineers",
    reportingTo: "Engineering Manager",
    travelRequired: "None",
    visaSponsorship: true,
    applicationStatus: "open",
    maxApplicants: 90
  },
  {
    id: 10,
    title: "Content Marketing Specialist",
    company: {
      id: 4,
      name: "FinanceFlow",
      logo: "/api/placeholder/60/60",
      industry: "Financial Technology",
      size: "51-200",
      rating: 4.6,
      verified: false
    },
    location: "New York, NY",
    type: "Full-time",
    remote: false,
    salary: {
      min: 65000,
      max: 85000,
      currency: "USD",
      period: "year"
    },
    experience: "Entry Level",
    description: "Create compelling content that drives engagement and growth for our fintech platform. Develop blog posts, social media content, and marketing materials that resonate with our target audience.",
    requirements: [
      "2+ years of content marketing experience",
      "Excellent writing and editing skills",
      "Experience with content management systems",
      "Knowledge of SEO best practices",
      "Familiarity with social media platforms",
      "Basic understanding of financial services"
    ],
    responsibilities: [
      "Create engaging blog posts and articles",
      "Develop social media content and campaigns",
      "Collaborate with design team on visual content",
      "Optimize content for search engines",
      "Analyze content performance and metrics",
      "Support email marketing campaigns"
    ],
    benefits: [
      "Competitive salary with performance bonuses",
      "Health and wellness benefits",
      "Professional development opportunities",
      "Flexible work arrangements",
      "Modern office in Manhattan",
      "Team building events and activities"
    ],
    skills: ["Content Writing", "SEO", "Social Media", "WordPress", "Google Analytics", "Email Marketing", "Copywriting", "Content Strategy"],
    department: "Marketing",
    posted: "1 week ago",
    deadline: "2024-02-22",
    applicants: 52,
    views: 890,
    featured: false,
    urgent: false,
    category: "Marketing",
    workModel: "Hybrid",
    education: "Bachelor's degree in Marketing, Communications, or related field",
    languages: ["English"],
    teamSize: "3-5 marketing professionals",
    reportingTo: "Marketing Manager",
    travelRequired: "Occasional industry events",
    visaSponsorship: false,
    applicationStatus: "open",
    maxApplicants: 70
  },
  {
    id: 11,
    title: "Clinical Research Coordinator",
    company: {
      id: 3,
      name: "HealthTech Solutions",
      logo: "/api/placeholder/60/60",
      industry: "Healthcare Technology",
      size: "1000+",
      rating: 4.7,
      verified: true
    },
    location: "Lilongwe, Malawi",
    locationDetails: {
      city: "Lilongwe",
      region: "Central Region",
      country: "Malawi",
      continent: "Africa",
      coordinates: {
        latitude: -13.9626,
        longitude: 33.7741
      }
    },
    type: "Full-time",
    remote: false,
    salary: {
      min: 55000,
      max: 75000,
      currency: "USD",
      period: "year"
    },
    experience: "Entry Level",
    description: "Support clinical research studies by coordinating patient recruitment, data collection, and regulatory compliance. Work with medical professionals to advance healthcare research.",
    requirements: [
      "Bachelor's degree in life sciences or related field",
      "1+ years of clinical research experience preferred",
      "Knowledge of GCP and regulatory requirements",
      "Strong attention to detail and organizational skills",
      "Excellent communication and interpersonal skills",
      "Proficiency in clinical data management systems"
    ],
    responsibilities: [
      "Coordinate patient recruitment and enrollment",
      "Collect and manage clinical trial data",
      "Ensure compliance with regulatory requirements",
      "Assist with study protocol implementation",
      "Communicate with study participants and families",
      "Maintain accurate study documentation"
    ],
    benefits: [
      "Competitive salary with annual increases",
      "Comprehensive health benefits",
      "Retirement savings plan with matching",
      "Professional development and training",
      "Flexible scheduling options",
      "Meaningful work improving patient outcomes"
    ],
    skills: ["Clinical Research", "GCP", "Data Management", "Regulatory Compliance", "Patient Care", "Documentation", "Medical Terminology", "Research Ethics"],
    department: "Clinical Research",
    posted: "5 days ago",
    deadline: "2024-02-28",
    applicants: 28,
    views: 450,
    featured: false,
    urgent: false,
    category: "Healthcare",
    workModel: "On-site",
    education: "Bachelor's degree in life sciences, nursing, or related field",
    languages: ["English"],
    teamSize: "6-8 research coordinators",
    reportingTo: "Clinical Research Manager",
    travelRequired: "Minimal",
    securityClearance: false,
    visaSponsorship: false,
    applicationStatus: "open",
    maxApplicants: 40
  },
  {
    id: 12,
    title: "Mobile App Developer",
    company: {
      id: 2,
      name: "DesignStudio Pro",
      logo: "/api/placeholder/60/60",
      industry: "Design & Creative",
      size: "51-200",
      rating: 4.9,
      verified: true
    },
    location: "San Francisco, CA",
    type: "Contract",
    remote: false,
    salary: {
      min: 80,
      max: 120,
      currency: "USD",
      period: "hour"
    },
    experience: "Senior Level",
    description: "Develop high-quality mobile applications for iOS and Android platforms. Work with our design team to create beautiful, user-friendly mobile experiences for our clients.",
    requirements: [
      "5+ years of mobile app development experience",
      "Proficiency in Swift/Objective-C and Kotlin/Java",
      "Experience with React Native or Flutter",
      "Strong understanding of mobile UI/UX principles",
      "Knowledge of mobile app deployment processes",
      "Portfolio of published mobile applications"
    ],
    responsibilities: [
      "Develop native and cross-platform mobile applications",
      "Collaborate with designers on UI/UX implementation",
      "Optimize app performance and user experience",
      "Integrate with backend APIs and services",
      "Conduct code reviews and testing",
      "Maintain and update existing mobile applications"
    ],
    benefits: [
      "Competitive hourly rate",
      "Flexible contract terms",
      "Opportunity to work on diverse projects",
      "Access to latest development tools",
      "Collaborative creative environment",
      "Potential for long-term partnership"
    ],
    skills: ["iOS Development", "Android Development", "React Native", "Flutter", "Swift", "Kotlin", "Mobile UI/UX", "API Integration"],
    department: "Development",
    posted: "3 days ago",
    deadline: "2024-02-20",
    applicants: 15,
    views: 320,
    featured: true,
    urgent: true,
    category: "Software Development",
    workModel: "On-site",
    education: "Bachelor's degree in Computer Science or equivalent experience",
    languages: ["English"],
    teamSize: "2-4 mobile developers",
    reportingTo: "Technical Director",
    travelRequired: "Client meetings as needed",
    visaSponsorship: false,
    applicationStatus: "closing-soon",
    maxApplicants: 35
  }
]

// Helper functions
export function getAllJobs(): Job[] {
  return jobs
}

export function getJobById(id: number): Job | undefined {
  return jobs.find(job => job.id === id)
}

export function getJobsByCompany(companyId: number): Job[] {
  return jobs.filter(job => job.company.id === companyId)
}

export function searchJobs(query: string): Job[] {
  const lowercaseQuery = query.toLowerCase()
  return jobs.filter(job => 
    job.title.toLowerCase().includes(lowercaseQuery) ||
    job.company.name.toLowerCase().includes(lowercaseQuery) ||
    job.location.toLowerCase().includes(lowercaseQuery) ||
    job.skills.some(skill => skill.toLowerCase().includes(lowercaseQuery)) ||
    job.category.toLowerCase().includes(lowercaseQuery)
  )
}

export function getJobsByCategory(category: string): Job[] {
  return jobs.filter(job => job.category === category)
}

export function getFeaturedJobs(): Job[] {
  return jobs.filter(job => job.featured)
}

export function getUrgentJobs(): Job[] {
  return jobs.filter(job => job.urgent)
}

export function getRemoteJobs(): Job[] {
  return jobs.filter(job => job.remote || job.workModel === 'Remote')
}

export function getApplicationStatusInfo(job: Job) {
  const { applicationStatus, applicants, maxApplicants, closingReason } = job

  switch (applicationStatus) {
    case 'open':
      return {
        status: 'open',
        label: 'Applications Open',
        color: 'green',
        canApply: true,
        message: maxApplicants ? `${applicants}/${maxApplicants} applications received` : `${applicants} applications received`,
        urgency: maxApplicants && applicants >= maxApplicants * 0.8 ? 'high' : 'normal'
      }
    case 'closing-soon':
      return {
        status: 'closing-soon',
        label: 'Closing Soon',
        color: 'orange',
        canApply: true,
        message: 'Applications closing soon - apply now!',
        urgency: 'high'
      }
    case 'closed':
      return {
        status: 'closed',
        label: 'Applications Closed',
        color: 'red',
        canApply: false,
        message: closingReason || 'This position is no longer accepting applications',
        urgency: 'none'
      }
    case 'filled':
      return {
        status: 'filled',
        label: 'Position Filled',
        color: 'gray',
        canApply: false,
        message: closingReason || 'This position has been filled',
        urgency: 'none'
      }
    default:
      return {
        status: 'open',
        label: 'Applications Open',
        color: 'green',
        canApply: true,
        message: `${applicants} applications received`,
        urgency: 'normal'
      }
  }
}
