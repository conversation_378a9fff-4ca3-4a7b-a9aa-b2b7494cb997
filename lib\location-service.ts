export interface LocationData {
  latitude: number
  longitude: number
  city: string
  region: string
  country: string
  continent: string
  countryCode: string
  regionCode: string
  accuracy?: number
  timestamp: number
}

export interface LocationHierarchy {
  local: string // City/District
  regional: string // Province/State
  national: string // Country
  continental: string // Africa, Europe, etc.
  international: string // Global
}

export interface GeolocationResult {
  success: boolean
  location?: LocationData
  error?: string
}

class LocationService {
  private static instance: LocationService
  private cachedLocation: LocationData | null = null
  private readonly CACHE_DURATION = 30 * 60 * 1000 // 30 minutes

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService()
    }
    return LocationService.instance
  }

  async getCurrentLocation(): Promise<GeolocationResult> {
    try {
      // Check if we have cached location that's still valid
      if (this.cachedLocation && this.isCacheValid()) {
        return { success: true, location: this.cachedLocation }
      }

      // Request geolocation permission
      const position = await this.getGeolocationPosition()
      
      // Get location details from coordinates
      const locationData = await this.getLocationFromCoordinates(
        position.coords.latitude,
        position.coords.longitude
      )

      // Cache the result
      this.cachedLocation = {
        ...locationData,
        accuracy: position.coords.accuracy,
        timestamp: Date.now()
      }

      // Save to localStorage for persistence
      this.saveLocationToStorage(this.cachedLocation)

      return { success: true, location: this.cachedLocation }
    } catch (error) {
      console.error('Location detection failed:', error)
      
      // Try to load from localStorage as fallback
      const savedLocation = this.loadLocationFromStorage()
      if (savedLocation) {
        this.cachedLocation = savedLocation
        return { success: true, location: savedLocation }
      }

      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Location detection failed' 
      }
    }
  }

  private getGeolocationPosition(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'))
        return
      }

      navigator.geolocation.getCurrentPosition(
        resolve,
        (error) => {
          switch (error.code) {
            case error.PERMISSION_DENIED:
              reject(new Error('Location access denied by user'))
              break
            case error.POSITION_UNAVAILABLE:
              reject(new Error('Location information is unavailable'))
              break
            case error.TIMEOUT:
              reject(new Error('Location request timed out'))
              break
            default:
              reject(new Error('An unknown error occurred'))
              break
          }
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      )
    })
  }

  private async getLocationFromCoordinates(lat: number, lng: number): Promise<LocationData> {
    try {
      // Using a free geocoding service (you might want to use a premium service for production)
      const response = await fetch(
        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`
      )
      
      if (!response.ok) {
        throw new Error('Geocoding service unavailable')
      }

      const data = await response.json()
      
      return {
        latitude: lat,
        longitude: lng,
        city: data.city || data.locality || 'Unknown City',
        region: data.principalSubdivision || data.region || 'Unknown Region',
        country: data.countryName || 'Unknown Country',
        continent: this.getContinent(data.countryCode || ''),
        countryCode: data.countryCode || '',
        regionCode: data.principalSubdivisionCode || '',
        timestamp: Date.now()
      }
    } catch (error) {
      // Fallback to basic location data
      return {
        latitude: lat,
        longitude: lng,
        city: 'Unknown City',
        region: 'Unknown Region',
        country: 'Unknown Country',
        continent: 'Unknown Continent',
        countryCode: '',
        regionCode: '',
        timestamp: Date.now()
      }
    }
  }

  private getContinent(countryCode: string): string {
    const continentMap: Record<string, string> = {
      // Africa
      'DZ': 'Africa', 'AO': 'Africa', 'BJ': 'Africa', 'BW': 'Africa', 'BF': 'Africa',
      'BI': 'Africa', 'CM': 'Africa', 'CV': 'Africa', 'CF': 'Africa', 'TD': 'Africa',
      'KM': 'Africa', 'CG': 'Africa', 'CD': 'Africa', 'CI': 'Africa', 'DJ': 'Africa',
      'EG': 'Africa', 'GQ': 'Africa', 'ER': 'Africa', 'ET': 'Africa', 'GA': 'Africa',
      'GM': 'Africa', 'GH': 'Africa', 'GN': 'Africa', 'GW': 'Africa', 'KE': 'Africa',
      'LS': 'Africa', 'LR': 'Africa', 'LY': 'Africa', 'MG': 'Africa', 'MW': 'Africa',
      'ML': 'Africa', 'MR': 'Africa', 'MU': 'Africa', 'MA': 'Africa', 'MZ': 'Africa',
      'NA': 'Africa', 'NE': 'Africa', 'NG': 'Africa', 'RW': 'Africa', 'ST': 'Africa',
      'SN': 'Africa', 'SC': 'Africa', 'SL': 'Africa', 'SO': 'Africa', 'ZA': 'Africa',
      'SS': 'Africa', 'SD': 'Africa', 'SZ': 'Africa', 'TZ': 'Africa', 'TG': 'Africa',
      'TN': 'Africa', 'UG': 'Africa', 'ZM': 'Africa', 'ZW': 'Africa',
      
      // Europe
      'AD': 'Europe', 'AL': 'Europe', 'AT': 'Europe', 'BY': 'Europe', 'BE': 'Europe',
      'BA': 'Europe', 'BG': 'Europe', 'HR': 'Europe', 'CY': 'Europe', 'CZ': 'Europe',
      'DK': 'Europe', 'EE': 'Europe', 'FI': 'Europe', 'FR': 'Europe', 'DE': 'Europe',
      'GR': 'Europe', 'HU': 'Europe', 'IS': 'Europe', 'IE': 'Europe', 'IT': 'Europe',
      'LV': 'Europe', 'LI': 'Europe', 'LT': 'Europe', 'LU': 'Europe', 'MK': 'Europe',
      'MT': 'Europe', 'MD': 'Europe', 'MC': 'Europe', 'ME': 'Europe', 'NL': 'Europe',
      'NO': 'Europe', 'PL': 'Europe', 'PT': 'Europe', 'RO': 'Europe', 'RU': 'Europe',
      'SM': 'Europe', 'RS': 'Europe', 'SK': 'Europe', 'SI': 'Europe', 'ES': 'Europe',
      'SE': 'Europe', 'CH': 'Europe', 'UA': 'Europe', 'GB': 'Europe', 'VA': 'Europe',
      
      // North America
      'US': 'North America', 'CA': 'North America', 'MX': 'North America',
      'GT': 'North America', 'BZ': 'North America', 'SV': 'North America',
      'HN': 'North America', 'NI': 'North America', 'CR': 'North America',
      'PA': 'North America', 'CU': 'North America', 'JM': 'North America',
      'HT': 'North America', 'DO': 'North America',
      
      // Asia
      'CN': 'Asia', 'IN': 'Asia', 'ID': 'Asia', 'PK': 'Asia', 'BD': 'Asia',
      'JP': 'Asia', 'PH': 'Asia', 'VN': 'Asia', 'TR': 'Asia', 'IR': 'Asia',
      'TH': 'Asia', 'MM': 'Asia', 'KR': 'Asia', 'IQ': 'Asia', 'AF': 'Asia',
      'SA': 'Asia', 'UZ': 'Asia', 'MY': 'Asia', 'NP': 'Asia', 'YE': 'Asia',
      'KP': 'Asia', 'LK': 'Asia', 'KZ': 'Asia', 'SY': 'Asia', 'KH': 'Asia',
      'JO': 'Asia', 'AZ': 'Asia', 'AE': 'Asia', 'TJ': 'Asia', 'IL': 'Asia',
      'LA': 'Asia', 'LB': 'Asia', 'SG': 'Asia', 'OM': 'Asia', 'KW': 'Asia',
      'GE': 'Asia', 'MN': 'Asia', 'AM': 'Asia', 'QA': 'Asia', 'BH': 'Asia',
      'BT': 'Asia', 'BN': 'Asia', 'MV': 'Asia',
      
      // South America
      'BR': 'South America', 'AR': 'South America', 'PE': 'South America',
      'CO': 'South America', 'VE': 'South America', 'CL': 'South America',
      'EC': 'South America', 'BO': 'South America', 'PY': 'South America',
      'UY': 'South America', 'GY': 'South America', 'SR': 'South America',
      'FK': 'South America',
      
      // Oceania
      'AU': 'Oceania', 'NZ': 'Oceania', 'PG': 'Oceania', 'FJ': 'Oceania',
      'SB': 'Oceania', 'VU': 'Oceania', 'WS': 'Oceania', 'KI': 'Oceania',
      'FM': 'Oceania', 'TO': 'Oceania', 'MH': 'Oceania', 'PW': 'Oceania',
      'NR': 'Oceania', 'TV': 'Oceania'
    }

    return continentMap[countryCode] || 'Unknown Continent'
  }

  getLocationHierarchy(location: LocationData): LocationHierarchy {
    return {
      local: location.city,
      regional: location.region,
      national: location.country,
      continental: location.continent,
      international: 'Global'
    }
  }

  private isCacheValid(): boolean {
    if (!this.cachedLocation) return false
    return Date.now() - this.cachedLocation.timestamp < this.CACHE_DURATION
  }

  private saveLocationToStorage(location: LocationData): void {
    try {
      localStorage.setItem('userLocation', JSON.stringify(location))
    } catch (error) {
      console.warn('Failed to save location to localStorage:', error)
    }
  }

  private loadLocationFromStorage(): LocationData | null {
    try {
      const saved = localStorage.getItem('userLocation')
      if (saved) {
        const location = JSON.parse(saved) as LocationData
        // Check if saved location is still valid (within cache duration)
        if (Date.now() - location.timestamp < this.CACHE_DURATION) {
          return location
        }
      }
    } catch (error) {
      console.warn('Failed to load location from localStorage:', error)
    }
    return null
  }

  clearLocationCache(): void {
    this.cachedLocation = null
    try {
      localStorage.removeItem('userLocation')
    } catch (error) {
      console.warn('Failed to clear location from localStorage:', error)
    }
  }

  // Calculate distance between two points (in kilometers)
  calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371 // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1)
    const dLng = this.toRadians(lng2 - lng1)
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }
}

export const locationService = LocationService.getInstance()
