import mongoose, { Document, Schema } from 'mongoose'

export interface IClient extends Document {
  _id: mongoose.Types.ObjectId
  user: mongoose.Types.ObjectId
  
  // Professional Information
  headline: string
  summary: string
  currentTitle?: string
  
  // Experience & Career
  experience: {
    level: 'entry' | 'mid' | 'senior' | 'executive'
    yearsOfExperience: number
    industries: string[]
    currentCompany?: string
    currentRole?: string
    currentSalary?: {
      amount: number
      currency: string
      period: 'hourly' | 'monthly' | 'yearly'
    }
  }
  
  // Work History
  workHistory: Array<{
    company: string
    position: string
    startDate: Date
    endDate?: Date
    isCurrent: boolean
    description: string
    achievements: string[]
    technologies: string[]
    location: {
      city: string
      state?: string
      country: string
      remote: boolean
    }
  }>
  
  // Education
  education: Array<{
    institution: string
    degree: string
    fieldOfStudy: string
    startDate: Date
    endDate?: Date
    isCurrent: boolean
    gpa?: number
    achievements: string[]
    location: {
      city: string
      state?: string
      country: string
    }
  }>
  
  // Skills & Expertise
  skills: Array<{
    name: string
    category: 'technical' | 'soft' | 'language' | 'tool' | 'framework'
    proficiency: 'beginner' | 'intermediate' | 'advanced' | 'expert'
    yearsOfExperience?: number
    endorsed: boolean
    endorsements: number
  }>
  
  // Certifications & Licenses
  certifications: Array<{
    name: string
    issuer: string
    issueDate: Date
    expiryDate?: Date
    credentialId?: string
    credentialUrl?: string
    isActive: boolean
  }>
  
  // Portfolio & Projects
  portfolio: Array<{
    title: string
    description: string
    url?: string
    imageUrl?: string
    technologies: string[]
    startDate: Date
    endDate?: Date
    isOngoing: boolean
    role: string
    achievements: string[]
  }>
  
  // Documents
  documents: {
    resume: Array<{
      filename: string
      url: string
      uploadDate: Date
      isActive: boolean
      fileSize: number
      fileType: string
    }>
    coverLetter?: {
      content: string
      lastUpdated: Date
    }
    portfolio?: {
      url: string
      lastUpdated: Date
    }
  }
  
  // Job Preferences
  jobPreferences: {
    desiredRoles: string[]
    industries: string[]
    locations: Array<{
      city: string
      state?: string
      country: string
      remote: boolean
      relocationWilling: boolean
    }>
    salaryExpectation: {
      min: number
      max: number
      currency: string
      period: 'hourly' | 'monthly' | 'yearly'
      negotiable: boolean
    }
    jobTypes: Array<'full-time' | 'part-time' | 'contract' | 'freelance' | 'internship'>
    workArrangement: Array<'remote' | 'hybrid' | 'onsite'>
    availability: 'immediately' | '2_weeks' | '1_month' | '3_months' | 'not_looking'
    benefits: string[]
    companySize: Array<'startup' | 'small' | 'medium' | 'large' | 'enterprise'>
  }
  
  // Languages
  languages: Array<{
    language: string
    proficiency: 'basic' | 'conversational' | 'fluent' | 'native'
    certified: boolean
  }>
  
  // Social & Professional Links
  socialLinks: {
    linkedin?: string
    github?: string
    portfolio?: string
    website?: string
    twitter?: string
    behance?: string
    dribbble?: string
    stackoverflow?: string
  }
  
  // Privacy & Visibility Settings
  privacy: {
    profileVisibility: 'public' | 'private' | 'recruiters_only'
    showSalaryExpectation: boolean
    showCurrentCompany: boolean
    allowRecruiterContact: boolean
    showProfileToCurrentEmployer: boolean
  }
  
  // Activity & Engagement
  activity: {
    profileViews: number
    searchAppearances: number
    recruiterViews: number
    lastProfileUpdate: Date
    profileCompleteness: number
  }
  
  // Application History
  applicationStats: {
    totalApplications: number
    pendingApplications: number
    interviewsReceived: number
    offersReceived: number
    successRate: number
  }
  
  // Saved Items
  savedJobs: mongoose.Types.ObjectId[]
  followedCompanies: mongoose.Types.ObjectId[]
  
  // Notifications & Alerts
  jobAlerts: Array<{
    name: string
    criteria: {
      keywords: string[]
      locations: string[]
      industries: string[]
      salaryMin?: number
      jobTypes: string[]
    }
    frequency: 'immediate' | 'daily' | 'weekly'
    isActive: boolean
    createdAt: Date
  }>
  
  // Verification & Trust
  verification: {
    emailVerified: boolean
    phoneVerified: boolean
    identityVerified: boolean
    backgroundCheckCompleted: boolean
    references: Array<{
      name: string
      position: string
      company: string
      email: string
      phone?: string
      relationship: string
      verified: boolean
    }>
  }
  
  // Metadata
  isActive: boolean
  isPublic: boolean
  lastLogin: Date
  createdAt: Date
  updatedAt: Date
}

// Sub-schemas
const WorkHistorySchema = new Schema({
  company: { type: String, required: true, trim: true },
  position: { type: String, required: true, trim: true },
  startDate: { type: Date, required: true },
  endDate: Date,
  isCurrent: { type: Boolean, default: false },
  description: { type: String, trim: true },
  achievements: [{ type: String, trim: true }],
  technologies: [{ type: String, trim: true }],
  location: {
    city: { type: String, required: true, trim: true },
    state: { type: String, trim: true },
    country: { type: String, required: true, trim: true },
    remote: { type: Boolean, default: false }
  }
}, { _id: false })

const EducationSchema = new Schema({
  institution: { type: String, required: true, trim: true },
  degree: { type: String, required: true, trim: true },
  fieldOfStudy: { type: String, required: true, trim: true },
  startDate: { type: Date, required: true },
  endDate: Date,
  isCurrent: { type: Boolean, default: false },
  gpa: { type: Number, min: 0, max: 4 },
  achievements: [{ type: String, trim: true }],
  location: {
    city: { type: String, required: true, trim: true },
    state: { type: String, trim: true },
    country: { type: String, required: true, trim: true }
  }
}, { _id: false })

const SkillSchema = new Schema({
  name: { type: String, required: true, trim: true },
  category: {
    type: String,
    enum: ['technical', 'soft', 'language', 'tool', 'framework'],
    required: true
  },
  proficiency: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced', 'expert'],
    required: true
  },
  yearsOfExperience: { type: Number, min: 0 },
  endorsed: { type: Boolean, default: false },
  endorsements: { type: Number, default: 0 }
}, { _id: false })

const CertificationSchema = new Schema({
  name: { type: String, required: true, trim: true },
  issuer: { type: String, required: true, trim: true },
  issueDate: { type: Date, required: true },
  expiryDate: Date,
  credentialId: { type: String, trim: true },
  credentialUrl: { type: String, trim: true },
  isActive: { type: Boolean, default: true }
}, { _id: false })

const PortfolioSchema = new Schema({
  title: { type: String, required: true, trim: true },
  description: { type: String, required: true, trim: true },
  url: { type: String, trim: true },
  imageUrl: { type: String, trim: true },
  technologies: [{ type: String, trim: true }],
  startDate: { type: Date, required: true },
  endDate: Date,
  isOngoing: { type: Boolean, default: false },
  role: { type: String, required: true, trim: true },
  achievements: [{ type: String, trim: true }]
}, { _id: false })

const DocumentSchema = new Schema({
  resume: [{
    filename: { type: String, required: true },
    url: { type: String, required: true },
    uploadDate: { type: Date, default: Date.now },
    isActive: { type: Boolean, default: true },
    fileSize: { type: Number, required: true },
    fileType: { type: String, required: true }
  }],
  coverLetter: {
    content: { type: String, trim: true },
    lastUpdated: { type: Date, default: Date.now }
  },
  portfolio: {
    url: { type: String, trim: true },
    lastUpdated: { type: Date, default: Date.now }
  }
}, { _id: false })

const JobPreferencesSchema = new Schema({
  desiredRoles: [{ type: String, trim: true }],
  industries: [{ type: String, trim: true }],
  locations: [{
    city: { type: String, required: true, trim: true },
    state: { type: String, trim: true },
    country: { type: String, required: true, trim: true },
    remote: { type: Boolean, default: false },
    relocationWilling: { type: Boolean, default: false }
  }],
  salaryExpectation: {
    min: { type: Number, required: true, min: 0 },
    max: { type: Number, required: true, min: 0 },
    currency: { type: String, default: 'USD' },
    period: { type: String, enum: ['hourly', 'monthly', 'yearly'], default: 'yearly' },
    negotiable: { type: Boolean, default: true }
  },
  jobTypes: [{
    type: String,
    enum: ['full-time', 'part-time', 'contract', 'freelance', 'internship']
  }],
  workArrangement: [{
    type: String,
    enum: ['remote', 'hybrid', 'onsite']
  }],
  availability: {
    type: String,
    enum: ['immediately', '2_weeks', '1_month', '3_months', 'not_looking'],
    default: 'immediately'
  },
  benefits: [{ type: String, trim: true }],
  companySize: [{
    type: String,
    enum: ['startup', 'small', 'medium', 'large', 'enterprise']
  }]
}, { _id: false })

const LanguageSchema = new Schema({
  language: { type: String, required: true, trim: true },
  proficiency: {
    type: String,
    enum: ['basic', 'conversational', 'fluent', 'native'],
    required: true
  },
  certified: { type: Boolean, default: false }
}, { _id: false })

const ClientSocialLinksSchema = new Schema({
  linkedin: { type: String, trim: true },
  github: { type: String, trim: true },
  portfolio: { type: String, trim: true },
  website: { type: String, trim: true },
  twitter: { type: String, trim: true },
  behance: { type: String, trim: true },
  dribbble: { type: String, trim: true },
  stackoverflow: { type: String, trim: true }
}, { _id: false })

const PrivacySchema = new Schema({
  profileVisibility: {
    type: String,
    enum: ['public', 'private', 'recruiters_only'],
    default: 'public'
  },
  showSalaryExpectation: { type: Boolean, default: true },
  showCurrentCompany: { type: Boolean, default: true },
  allowRecruiterContact: { type: Boolean, default: true },
  showProfileToCurrentEmployer: { type: Boolean, default: false }
}, { _id: false })

const ActivitySchema = new Schema({
  profileViews: { type: Number, default: 0 },
  searchAppearances: { type: Number, default: 0 },
  recruiterViews: { type: Number, default: 0 },
  lastProfileUpdate: { type: Date, default: Date.now },
  profileCompleteness: { type: Number, default: 0, min: 0, max: 100 }
}, { _id: false })

const ApplicationStatsSchema = new Schema({
  totalApplications: { type: Number, default: 0 },
  pendingApplications: { type: Number, default: 0 },
  interviewsReceived: { type: Number, default: 0 },
  offersReceived: { type: Number, default: 0 },
  successRate: { type: Number, default: 0, min: 0, max: 100 }
}, { _id: false })

const JobAlertSchema = new Schema({
  name: { type: String, required: true, trim: true },
  criteria: {
    keywords: [{ type: String, trim: true }],
    locations: [{ type: String, trim: true }],
    industries: [{ type: String, trim: true }],
    salaryMin: { type: Number, min: 0 },
    jobTypes: [{ type: String, trim: true }]
  },
  frequency: {
    type: String,
    enum: ['immediate', 'daily', 'weekly'],
    default: 'daily'
  },
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now }
}, { _id: false })

const ReferenceSchema = new Schema({
  name: { type: String, required: true, trim: true },
  position: { type: String, required: true, trim: true },
  company: { type: String, required: true, trim: true },
  email: { type: String, required: true, lowercase: true, trim: true },
  phone: { type: String, trim: true },
  relationship: { type: String, required: true, trim: true },
  verified: { type: Boolean, default: false }
}, { _id: false })

const ClientVerificationSchema = new Schema({
  emailVerified: { type: Boolean, default: false },
  phoneVerified: { type: Boolean, default: false },
  identityVerified: { type: Boolean, default: false },
  backgroundCheckCompleted: { type: Boolean, default: false },
  references: [ReferenceSchema]
}, { _id: false })

const ExperienceSchema = new Schema({
  level: {
    type: String,
    enum: ['entry', 'mid', 'senior', 'executive'],
    required: true
  },
  yearsOfExperience: { type: Number, required: true, min: 0 },
  industries: [{ type: String, trim: true }],
  currentCompany: { type: String, trim: true },
  currentRole: { type: String, trim: true },
  currentSalary: {
    amount: { type: Number, min: 0 },
    currency: { type: String, default: 'USD' },
    period: { type: String, enum: ['hourly', 'monthly', 'yearly'], default: 'yearly' }
  }
}, { _id: false })

// Main Client Schema
const ClientSchema = new Schema<IClient>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User reference is required'],
    unique: true
  },

  // Professional Information
  headline: {
    type: String,
    required: [true, 'Professional headline is required'],
    trim: true,
    maxlength: [200, 'Headline cannot exceed 200 characters']
  },
  summary: {
    type: String,
    required: [true, 'Professional summary is required'],
    trim: true,
    maxlength: [2000, 'Summary cannot exceed 2000 characters']
  },
  currentTitle: {
    type: String,
    trim: true,
    maxlength: [100, 'Current title cannot exceed 100 characters']
  },

  // Experience & Career
  experience: {
    type: ExperienceSchema,
    required: [true, 'Experience information is required']
  },

  // Work History
  workHistory: [WorkHistorySchema],

  // Education
  education: [EducationSchema],

  // Skills & Expertise
  skills: [SkillSchema],

  // Certifications & Licenses
  certifications: [CertificationSchema],

  // Portfolio & Projects
  portfolio: [PortfolioSchema],

  // Documents
  documents: {
    type: DocumentSchema,
    default: () => ({})
  },

  // Job Preferences
  jobPreferences: {
    type: JobPreferencesSchema,
    required: [true, 'Job preferences are required']
  },

  // Languages
  languages: [LanguageSchema],

  // Social & Professional Links
  socialLinks: {
    type: ClientSocialLinksSchema,
    default: () => ({})
  },

  // Privacy & Visibility Settings
  privacy: {
    type: PrivacySchema,
    default: () => ({})
  },

  // Activity & Engagement
  activity: {
    type: ActivitySchema,
    default: () => ({})
  },

  // Application History
  applicationStats: {
    type: ApplicationStatsSchema,
    default: () => ({})
  },

  // Saved Items
  savedJobs: [{
    type: Schema.Types.ObjectId,
    ref: 'Job'
  }],
  followedCompanies: [{
    type: Schema.Types.ObjectId,
    ref: 'Company'
  }],

  // Notifications & Alerts
  jobAlerts: [JobAlertSchema],

  // Verification & Trust
  verification: {
    type: ClientVerificationSchema,
    default: () => ({})
  },

  // Metadata
  isActive: {
    type: Boolean,
    default: true
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes for better performance (user field already has unique index)
ClientSchema.index({ 'experience.level': 1 })
ClientSchema.index({ 'experience.industries': 1 })
ClientSchema.index({ 'skills.name': 1 })
ClientSchema.index({ 'jobPreferences.desiredRoles': 1 })
ClientSchema.index({ 'jobPreferences.locations.city': 1 })
ClientSchema.index({ 'privacy.profileVisibility': 1 })
ClientSchema.index({ isActive: 1, isPublic: 1 })

// Virtual for full name
ClientSchema.virtual('fullName').get(function() {
  return `${this.user?.profile?.firstName} ${this.user?.profile?.lastName}`.trim()
})

// Method to calculate profile completeness
ClientSchema.methods.calculateProfileCompleteness = function() {
  let score = 0
  const maxScore = 100

  // Basic info (20 points)
  if (this.headline) score += 5
  if (this.summary) score += 10
  if (this.currentTitle) score += 5

  // Experience (20 points)
  if (this.experience?.level) score += 5
  if (this.experience?.yearsOfExperience >= 0) score += 5
  if (this.workHistory?.length > 0) score += 10

  // Education (15 points)
  if (this.education?.length > 0) score += 15

  // Skills (15 points)
  if (this.skills?.length >= 5) score += 15
  else if (this.skills?.length > 0) score += 10

  // Documents (10 points)
  if (this.documents?.resume?.length > 0) score += 10

  // Job Preferences (10 points)
  if (this.jobPreferences?.desiredRoles?.length > 0) score += 5
  if (this.jobPreferences?.salaryExpectation?.min > 0) score += 5

  // Additional (10 points)
  if (this.portfolio?.length > 0) score += 5
  if (this.certifications?.length > 0) score += 3
  if (this.languages?.length > 0) score += 2

  this.activity.profileCompleteness = Math.min(score, maxScore)
  return this.activity.profileCompleteness
}

// Pre-save middleware to update profile completeness
ClientSchema.pre('save', function(next) {
  this.calculateProfileCompleteness()
  next()
})

export const Client = mongoose.models.Client || mongoose.model<IClient>('Client', ClientSchema)
