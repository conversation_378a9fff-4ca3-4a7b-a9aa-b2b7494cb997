import mongoose, { Document, Schema } from 'mongoose'
import bcrypt from 'bcryptjs'

export interface IUser extends Document {
  _id: mongoose.Types.ObjectId
  email: string
  password: string
  role: 'admin' | 'company_admin' | 'recruiter' | 'job_seeker'
  profile: {
    firstName: string
    lastName: string
    avatar?: string
    phone?: string
    location: {
      city?: string
      state?: string
      country?: string
      coordinates?: [number, number] // [longitude, latitude]
    }
  }
  preferences: {
    emailNotifications: boolean
    jobAlerts: boolean
    marketingEmails: boolean
    theme: 'light' | 'dark' | 'system'
  }
  subscription?: {
    plan: 'free' | 'premium' | 'enterprise'
    status: 'active' | 'inactive' | 'cancelled'
    stripeCustomerId?: string
    stripeSubscriptionId?: string
    currentPeriodEnd?: Date
  }
  companyId?: mongoose.Types.ObjectId
  isActive: boolean
  isEmailVerified: boolean
  emailVerificationToken?: string
  passwordResetToken?: string
  passwordResetExpires?: Date
  lastLogin?: Date
  createdAt: Date
  updatedAt: Date
  
  // Methods
  comparePassword(candidatePassword: string): Promise<boolean>
  generatePasswordResetToken(): string
}

const LocationSchema = new Schema({
  city: { type: String, trim: true },
  state: { type: String, trim: true },
  country: { type: String, trim: true },
  coordinates: {
    type: [Number]
  }
}, { _id: false })

const PreferencesSchema = new Schema({
  emailNotifications: { type: Boolean, default: true },
  jobAlerts: { type: Boolean, default: true },
  marketingEmails: { type: Boolean, default: false },
  theme: { 
    type: String, 
    enum: ['light', 'dark', 'system'], 
    default: 'system' 
  }
}, { _id: false })

const SubscriptionSchema = new Schema({
  plan: { 
    type: String, 
    enum: ['free', 'premium', 'enterprise'], 
    default: 'free' 
  },
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'cancelled'], 
    default: 'active' 
  },
  stripeCustomerId: { type: String },
  stripeSubscriptionId: { type: String },
  currentPeriodEnd: { type: Date }
}, { _id: false })

const ProfileSchema = new Schema({
  firstName: { 
    type: String, 
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  lastName: { 
    type: String, 
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  avatar: { type: String },
  phone: { 
    type: String,
    trim: true,
    validate: {
      validator: function(v: string) {
        return !v || /^\+?[\d\s\-\(\)]+$/.test(v)
      },
      message: 'Invalid phone number format'
    }
  },
  location: LocationSchema
}, { _id: false })

const UserSchema = new Schema<IUser>({
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    trim: true,
    validate: {
      validator: function(v: string) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v)
      },
      message: 'Invalid email format'
    }
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters long'],
    select: false // Don't include password in queries by default
  },
  role: {
    type: String,
    enum: ['admin', 'company_admin', 'recruiter', 'job_seeker'],
    default: 'job_seeker',
    required: true
  },
  profile: {
    type: ProfileSchema,
    required: true
  },
  preferences: {
    type: PreferencesSchema,
    default: () => ({})
  },
  subscription: SubscriptionSchema,
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date,
  lastLogin: Date
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      delete ret.password
      delete ret.passwordResetToken
      delete ret.emailVerificationToken
      return ret
    }
  },
  toObject: { virtuals: true }
})

// Indexes
UserSchema.index({ email: 1 }, { unique: true })
UserSchema.index({ role: 1, isActive: 1 })
UserSchema.index({ companyId: 1 })
UserSchema.index({ 'profile.location.coordinates': '2dsphere' })
UserSchema.index({ createdAt: -1 })

// Virtual for full name
UserSchema.virtual('profile.fullName').get(function() {
  return `${this.profile.firstName} ${this.profile.lastName}`
})

// Pre-save middleware to hash password
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next()
  
  try {
    const salt = await bcrypt.genSalt(12)
    this.password = await bcrypt.hash(this.password, salt)
    next()
  } catch (error) {
    next(error as Error)
  }
})

// Method to compare password
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password)
}

// Method to generate password reset token
UserSchema.methods.generatePasswordResetToken = function(): string {
  const resetToken = Math.random().toString(36).substring(2, 15) + 
                    Math.random().toString(36).substring(2, 15)
  
  this.passwordResetToken = resetToken
  this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
  
  return resetToken
}

export const User = mongoose.models.User || mongoose.model<IUser>('User', UserSchema)
