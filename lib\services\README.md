# Service Layer Architecture

This directory contains the service layer implementation for the Job Portal application. The service layer provides a clean separation of concerns by extracting business logic from API routes.

## Architecture Overview

```
app/api/v1/
├── auth/
│   ├── login/route.ts      # Thin API layer
│   └── register/route.ts   # Thin API layer
├── users/
│   ├── route.ts           # Users collection endpoint
│   └── [id]/route.ts      # Individual user endpoint
└── companies/
    ├── route.ts           # Companies collection endpoint
    └── [id]/route.ts      # Individual company endpoint

lib/services/
├── base.service.ts        # Base service with common functionality
├── auth.service.ts        # Authentication business logic
├── user.service.ts        # User management business logic
├── company.service.ts     # Company management business logic
├── validation.service.ts  # Input validation logic
└── index.ts              # Service exports
```

## Design Principles

### 1. Separation of Concerns
- **API Routes**: Handle HTTP concerns (request/response, validation, error handling)
- **Services**: Handle business logic, data processing, and database operations
- **Models**: Handle data structure and basic validation

### 2. Single Responsibility
Each service class has a single, well-defined responsibility:
- `AuthService`: User authentication and authorization
- `UserService`: User profile management
- `CompanyService`: Company profile management
- `ValidationService`: Input validation and sanitization

### 3. Dependency Injection
Services are exported as singletons and can be easily mocked for testing:
```typescript
import { authService, userService } from '@/lib/services'
```

## Service Structure

### Base Service
All services extend `BaseService` which provides common functionality:
- Pagination helpers
- Validation utilities
- Error handling
- Database error management
- Permission checking

### Service Methods
Each service follows consistent patterns:
- **Public methods**: Business operations exposed to API routes
- **Private methods**: Internal helper methods (prefixed with `_` or `private`)
- **Error handling**: Consistent error throwing using `errorService`
- **Type safety**: Full TypeScript support with interfaces

## Usage Examples

### API Route Implementation
```typescript
// app/api/v1/auth/login/route.ts
import { authService, validationService } from '@/lib/services'

export const POST = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['POST'])
  
  const loginData = await validateRequestBody(request, validationService.validateLoginRequest)
  const result = await authService.login(loginData)
  
  return createSuccessResponse(result)
})
```

### Service Implementation
```typescript
// lib/services/auth.service.ts
export class AuthService {
  async login(loginData: LoginRequest): Promise<AuthResponse> {
    // Business logic here
    const user = await User.findOne({ email: loginData.email })
    // ... authentication logic
    return this.formatAuthResponse(user, tokens)
  }
}
```

## Benefits

### 1. Testability
Services can be easily unit tested in isolation:
```typescript
import { authService } from '@/lib/services'

describe('AuthService', () => {
  it('should authenticate valid user', async () => {
    const result = await authService.login(validLoginData)
    expect(result.user.email).toBe(validLoginData.email)
  })
})
```

### 2. Reusability
Business logic can be reused across different API endpoints or even different interfaces (GraphQL, CLI, etc.)

### 3. Maintainability
- Clear separation makes code easier to understand and modify
- Changes to business logic don't affect API route structure
- Consistent error handling and validation patterns

### 4. Type Safety
Full TypeScript support with interfaces for all service methods and data structures

## Service Guidelines

### 1. Error Handling
Always use `errorService` for consistent error handling:
```typescript
throw errorService.createError(
  ErrorCode.NOT_FOUND,
  'User not found',
  'userId'
)
```

### 2. Validation
Use `validationService` or create specific validation methods:
```typescript
const userData = validationService.validateRegisterRequest(data)
```

### 3. Database Operations
Handle database errors consistently:
```typescript
try {
  await user.save()
} catch (error) {
  this.handleDatabaseError(error, 'createUser')
}
```

### 4. Response Formatting
Create consistent response formats:
```typescript
private formatUserProfile(user: any): UserProfile {
  return {
    id: user._id.toString(),
    email: user.email,
    // ... other fields
  }
}
```

## Adding New Services

1. Create service class extending `BaseService`
2. Define interfaces for requests and responses
3. Implement business logic methods
4. Add to `index.ts` exports
5. Create corresponding API routes
6. Write unit tests

Example:
```typescript
// lib/services/job.service.ts
export class JobService extends BaseService {
  async createJob(jobData: CreateJobRequest): Promise<JobProfile> {
    // Implementation
  }
}

export const jobService = new JobService()
```

## Testing

Services should be thoroughly tested with:
- Unit tests for individual methods
- Integration tests for database operations
- Mock tests for external dependencies

```typescript
// __tests__/services/auth.service.test.ts
import { authService } from '@/lib/services'

describe('AuthService', () => {
  // Test cases
})
```

This architecture provides a solid foundation for scalable, maintainable, and testable code.
