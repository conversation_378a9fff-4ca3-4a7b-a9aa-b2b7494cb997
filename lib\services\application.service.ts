import BaseService from './base.service'
import { Application } from '@/lib/models/application.model'
import { Job } from '@/lib/models/job.model'
import { User } from '@/lib/models/user.model'
import { cacheService } from './cache.service'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'

export interface ApplicationProfile {
  id: string
  job: {
    id: string
    title: string
    company: {
      id: string
      name: string
    }
  }
  applicant: {
    id: string
    firstName: string
    lastName: string
    email: string
    avatar?: string
  }
  status: 'pending' | 'reviewing' | 'shortlisted' | 'interviewed' | 'offered' | 'hired' | 'rejected' | 'withdrawn'
  coverLetter?: string
  resumeUrl?: string
  portfolioUrl?: string
  expectedSalary?: {
    amount: number
    currency: string
    period: 'hourly' | 'monthly' | 'yearly'
  }
  availableStartDate?: Date
  customAnswers?: Array<{
    question: string
    answer: string
  }>
  notes?: Array<{
    content: string
    createdBy: {
      id: string
      name: string
    }
    createdAt: Date
  }>
  timeline: Array<{
    status: string
    changedBy: {
      id: string
      name: string
    }
    changedAt: Date
    notes?: string
  }>
  createdAt: Date
  updatedAt: Date
}

export interface CreateApplicationRequest {
  jobId: string
  coverLetter?: string
  resumeUrl?: string
  portfolioUrl?: string
  expectedSalary?: {
    amount: number
    currency: string
    period: 'hourly' | 'monthly' | 'yearly'
  }
  availableStartDate?: Date
  customAnswers?: Array<{
    question: string
    answer: string
  }>
}

export interface UpdateApplicationStatusRequest {
  status: 'pending' | 'reviewing' | 'shortlisted' | 'interviewed' | 'offered' | 'hired' | 'rejected'
  notes?: string
}

export interface AddNoteRequest {
  content: string
}

export class ApplicationService extends BaseService {
  /**
   * Create a new job application
   */
  async createApplication(applicationData: CreateApplicationRequest, applicantId: string): Promise<ApplicationProfile> {
    try {
      this.validateRequiredFields(applicationData, ['jobId'])
      this.validateObjectId(applicationData.jobId, 'jobId')
      this.validateObjectId(applicantId, 'applicantId')

      // Verify job exists and is active
      const job = await Job.findById(applicationData.jobId).populate('companyId', 'name')
      if (!job || !job.isActive) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Job not found or no longer active',
          'jobId'
        )
      }

      // Check if application deadline has passed
      if (job.applicationDeadline && job.applicationDeadline < new Date()) {
        throw errorService.createError(
          ErrorCode.VALIDATION_ERROR,
          'Application deadline has passed',
          'deadline'
        )
      }

      // Check if user already applied for this job
      const existingApplication = await Application.findOne({
        jobId: applicationData.jobId,
        applicantId
      })

      if (existingApplication) {
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'You have already applied for this job',
          'application'
        )
      }

      // Verify applicant exists and is a job seeker
      const applicant = await User.findById(applicantId)
      if (!applicant || applicant.role !== 'job_seeker') {
        throw errorService.createError(
          ErrorCode.FORBIDDEN,
          'Only job seekers can apply for jobs',
          'role'
        )
      }

      // Create application
      const application = new Application({
        ...applicationData,
        applicantId,
        status: 'pending',
        timeline: [{
          status: 'pending',
          changedBy: applicantId,
          changedAt: new Date(),
          notes: 'Application submitted'
        }]
      })

      await application.save()

      // Increment job applications count
      await Job.findByIdAndUpdate(applicationData.jobId, { $inc: { applicationsCount: 1 } })

      // Clear related caches
      await this.clearApplicationCaches(applicantId, applicationData.jobId)

      // Populate for response
      await application.populate([
        {
          path: 'jobId',
          select: 'title companyId',
          populate: {
            path: 'companyId',
            select: 'name'
          }
        },
        {
          path: 'applicantId',
          select: 'profile.firstName profile.lastName email profile.avatar'
        },
        {
          path: 'timeline.changedBy',
          select: 'profile.firstName profile.lastName'
        }
      ])

      const result = this.formatApplicationProfile(application)
      this.logOperation('createApplication', { applicationId: result.id, jobId: applicationData.jobId })

      return result

    } catch (error) {
      this.handleDatabaseError(error, 'createApplication')
    }
  }

  /**
   * Get application by ID
   */
  async getApplicationById(applicationId: string, userId: string): Promise<ApplicationProfile> {
    this.validateObjectId(applicationId, 'applicationId')

    const application = await Application.findById(applicationId)
      .populate([
        {
          path: 'jobId',
          select: 'title companyId createdBy',
          populate: {
            path: 'companyId',
            select: 'name'
          }
        },
        {
          path: 'applicantId',
          select: 'profile.firstName profile.lastName email profile.avatar'
        },
        {
          path: 'timeline.changedBy',
          select: 'profile.firstName profile.lastName'
        },
        {
          path: 'notes.createdBy',
          select: 'profile.firstName profile.lastName'
        }
      ])

    if (!application) {
      this.createNotFoundError('Application', applicationId)
    }

    // Check permissions
    const user = await User.findById(userId)
    const isApplicant = application.applicantId._id.toString() === userId
    const isCompanyAdmin = user?.companyId?.toString() === application.jobId.companyId._id.toString()
    const isAdmin = user && ['admin', 'super_admin'].includes(user.role)

    if (!isApplicant && !isCompanyAdmin && !isAdmin) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'You do not have permission to view this application',
        'permission'
      )
    }

    return this.formatApplicationProfile(application)
  }

  /**
   * Get applications for a job (company view)
   */
  async getJobApplications(
    jobId: string,
    userId: string,
    page: number = 1,
    limit: number = 10,
    status?: string
  ): Promise<{
    applications: ApplicationProfile[]
    pagination: any
  }> {
    this.validateObjectId(jobId, 'jobId')
    const { page: validPage, limit: validLimit } = this.validatePaginationParams(page, limit)

    // Verify job exists and user has permission
    const job = await Job.findById(jobId).populate('companyId')
    if (!job) {
      this.createNotFoundError('Job', jobId)
    }

    const user = await User.findById(userId)
    const isCompanyAdmin = user?.companyId?.toString() === job.companyId._id.toString()
    const isAdmin = user && ['admin', 'super_admin'].includes(user.role)

    if (!isCompanyAdmin && !isAdmin) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'You do not have permission to view applications for this job',
        'permission'
      )
    }

    // Try cache first
    const cacheKey = cacheService.keys.jobApplications(jobId, validPage, validLimit)
    const cached = await cacheService.get(cacheKey)

    if (cached && !status) {
      return cached
    }

    const query: any = { jobId }
    if (status) {
      query.status = status
    }

    const skip = (validPage - 1) * validLimit

    const [applications, total] = await Promise.all([
      Application.find(query)
        .populate([
          {
            path: 'applicantId',
            select: 'profile.firstName profile.lastName email profile.avatar'
          },
          {
            path: 'timeline.changedBy',
            select: 'profile.firstName profile.lastName'
          }
        ])
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(validLimit),
      Application.countDocuments(query)
    ])

    const result = {
      applications: applications.map(app => this.formatApplicationProfile(app)),
      pagination: this.createPaginationMeta(validPage, validLimit, total)
    }

    // Cache for 5 minutes if no status filter
    if (!status) {
      await cacheService.set(cacheKey, result, 5 * 60)
    }

    return result
  }

  /**
   * Get applications by user (applicant view)
   */
  async getUserApplications(
    userId: string,
    page: number = 1,
    limit: number = 10,
    status?: string
  ): Promise<{
    applications: ApplicationProfile[]
    pagination: any
  }> {
    this.validateObjectId(userId, 'userId')
    const { page: validPage, limit: validLimit } = this.validatePaginationParams(page, limit)

    // Try cache first
    const cacheKey = cacheService.keys.userApplications(userId, validPage, validLimit)
    const cached = await cacheService.get(cacheKey)

    if (cached && !status) {
      return cached
    }

    const query: any = { applicantId: userId }
    if (status) {
      query.status = status
    }

    const skip = (validPage - 1) * validLimit

    const [applications, total] = await Promise.all([
      Application.find(query)
        .populate([
          {
            path: 'jobId',
            select: 'title companyId',
            populate: {
              path: 'companyId',
              select: 'name'
            }
          }
        ])
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(validLimit),
      Application.countDocuments(query)
    ])

    const result = {
      applications: applications.map(app => this.formatApplicationProfile(app)),
      pagination: this.createPaginationMeta(validPage, validLimit, total)
    }

    // Cache for 5 minutes if no status filter
    if (!status) {
      await cacheService.set(cacheKey, result, 5 * 60)
    }

    return result
  }

  /**
   * Format application data for API response
   */
  private formatApplicationProfile(application: any): ApplicationProfile {
    return {
      id: application._id.toString(),
      job: {
        id: application.jobId._id.toString(),
        title: application.jobId.title,
        company: {
          id: application.jobId.companyId._id.toString(),
          name: application.jobId.companyId.name
        }
      },
      applicant: {
        id: application.applicantId._id.toString(),
        firstName: application.applicantId.profile.firstName,
        lastName: application.applicantId.profile.lastName,
        email: application.applicantId.email,
        avatar: application.applicantId.profile.avatar
      },
      status: application.status,
      coverLetter: application.coverLetter,
      resumeUrl: application.resumeUrl,
      portfolioUrl: application.portfolioUrl,
      expectedSalary: application.expectedSalary,
      availableStartDate: application.availableStartDate,
      customAnswers: application.customAnswers,
      notes: application.notes?.map((note: any) => ({
        content: note.content,
        createdBy: {
          id: note.createdBy._id.toString(),
          name: `${note.createdBy.profile.firstName} ${note.createdBy.profile.lastName}`
        },
        createdAt: note.createdAt
      })),
      timeline: application.timeline.map((entry: any) => ({
        status: entry.status,
        changedBy: {
          id: entry.changedBy._id.toString(),
          name: `${entry.changedBy.profile.firstName} ${entry.changedBy.profile.lastName}`
        },
        changedAt: entry.changedAt,
        notes: entry.notes
      })),
      createdAt: application.createdAt,
      updatedAt: application.updatedAt
    }
  }

  /**
   * Clear application-related caches
   */
  private async clearApplicationCaches(userId: string, jobId: string): Promise<void> {
    const patterns = [
      `applications:user:${userId}:*`,
      `applications:job:${jobId}:*`
    ]

    for (const pattern of patterns) {
      const keys = await cacheService.keys(pattern)
      for (const key of keys) {
        await cacheService.del(key)
      }
    }
  }
}

export const applicationService = new ApplicationService()
