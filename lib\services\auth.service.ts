import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { Client } from '@/lib/models/client.model'
import { generateTokens } from '@/lib/auth/middleware'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { normalizeWebsiteUrl } from '@/lib/utils'

export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterRequest {
  email: string
  password: string
  firstName: string
  lastName: string
  role?: 'job_seeker' | 'company_admin'
  phone?: string
  location?: {
    city?: string
    state?: string
    country?: string
  }
  company?: {
    name: string
    description?: string
    website?: string
    industry: string
    size: string
    location?: {
      city: string
      state?: string
      country: string
    }
  }
}

export interface AuthResponse {
  user: {
    id: string
    email: string
    role: string
    profile: {
      firstName: string
      lastName: string
      fullName: string
      avatar?: string
    }
    isEmailVerified: boolean
    companyId?: string
  }
  tokens: {
    accessToken: string
    refreshToken: string
  }
  company?: {
    _id: string
    name: string
    slug: string
    description: string
    website?: string
    industry: string[]
    size: string
    verification: any
  }
  client?: {
    _id: string
    headline: string
    summary: string
    experience: any
    profileCompleteness: number
  }
  emailVerificationRequired?: boolean
}

export class AuthService {
  /**
   * Authenticate user with email and password
   */
  async login(loginData: LoginRequest): Promise<AuthResponse> {
    try {
      // Find user by email and include password for comparison
      const user = await User.findOne({ 
        email: loginData.email 
      }).select('+password').populate('companyId', 'name slug')
      
      if (!user) {
        throw errorService.createError(
          ErrorCode.INVALID_CREDENTIALS,
          'Invalid email or password',
          'email'
        )
      }
      
      // Check if account is active
      if (!user.isActive) {
        throw errorService.createError(
          ErrorCode.FORBIDDEN,
          'Account has been deactivated. Please contact support.',
          'account'
        )
      }
      
      // Verify password
      const isPasswordValid = await user.comparePassword(loginData.password)
      if (!isPasswordValid) {
        throw errorService.createError(
          ErrorCode.INVALID_CREDENTIALS,
          'Invalid email or password',
          'password'
        )
      }
      
      // Update last login
      user.lastLogin = new Date()
      await user.save()
      
      // Generate tokens
      const tokens = generateTokens(user)
      
      return await this.formatAuthResponse(user, tokens)
      
    } catch (error: unknown) {
      // Don't expose specific error details for security
      if (error instanceof Error && error.message.includes('Invalid email or password')) {
        throw error
      }
      
      // Log the actual error but return generic message
      console.error('Login error:', error)
      throw errorService.createError(
        ErrorCode.INVALID_CREDENTIALS,
        'Invalid email or password'
      )
    }
  }

  /**
   * Register a new user
   */
  async register(registerData: RegisterRequest): Promise<AuthResponse> {
    try {
      // Check if user already exists
      const existingUser = await User.findOne({ email: registerData.email })
      if (existingUser) {
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'An account with this email already exists',
          'email'
        )
      }
      
      // Create new user
      const user = new User({
        email: registerData.email,
        password: registerData.password,
        role: registerData.role || 'job_seeker',
        profile: {
          firstName: registerData.firstName,
          lastName: registerData.lastName,
          phone: registerData.phone,
          location: registerData.location || {}
        },
        preferences: {
          emailNotifications: true,
          jobAlerts: true,
          marketingEmails: false,
          theme: 'system'
        },
        isActive: true,
        isEmailVerified: false // Will be verified via email
      })
      
      await user.save()

      // Create company if user is company admin
      if (registerData.role === 'company_admin' && registerData.company) {
        try {
          const company = await this.createCompanyForUser(user, registerData.company)
          user.companyId = company._id
          await user.save()
        } catch (companyError) {
          console.error('Company creation error:', companyError)
          // Continue with user registration even if company creation fails
        }
      }

      // Create client profile for job seekers
      if (registerData.role === 'job_seeker') {
        try {
          await this.createClientForUser(user, registerData.location)
        } catch (clientError) {
          console.error('Client creation error:', clientError)
          // Continue with user registration even if client creation fails
        }
      }

      // Generate tokens
      const tokens = generateTokens(user)

      // TODO: Send email verification email
      // await emailService.sendVerificationEmail(user)

      return await this.formatAuthResponse(user, tokens)
      
    } catch (error: unknown) {
      // Handle specific database errors
      if (typeof error === 'object' && error !== null && 'code' in error && (error as any).code === 11000) {
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'An account with this email already exists',
          'email'
        )
      }

      throw error
    }
  }

  /**
   * Create company for company admin user
   */
  private async createCompanyForUser(user: any, companyData: any): Promise<any> {
    const newCompany = new Company({
      name: companyData.name,
      slug: companyData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
      description: companyData.description || `${companyData.name} - A growing company`,
      tagline: `Welcome to ${companyData.name}`,
      website: companyData.website ? normalizeWebsiteUrl(companyData.website) : undefined,
      industry: [companyData.industry],
      size: companyData.size,
      contact: {
        email: user.email,
        phone: user.profile.phone || '',
        address: ''
      },
      locations: [{
        city: companyData.location?.city || user.profile.location?.city || '',
        state: companyData.location?.state || user.profile.location?.state || '',
        country: companyData.location?.country || user.profile.location?.country || '',
        isHeadquarters: true,
        address: '',
        postalCode: ''
      }],
      culture: {
        values: [],
        benefits: [],
        workEnvironment: 'Collaborative and innovative',
        diversity: 'We value diversity and inclusion',
        mission: `${companyData.name} is committed to excellence and innovation`,
        vision: 'To be a leader in our industry',
        perks: []
      },
      socialLinks: {},
      subscription: {
        plan: 'starter',
        status: 'trial',
        jobPostingLimit: 5,
        jobPostingsUsed: 0,
        featuredJobsLimit: 1,
        featuredJobsUsed: 0
      },
      admins: [user._id],
      recruiters: [],
      teamMembers: [{
        user: user._id,
        role: 'Company Admin',
        department: 'Management',
        joinedAt: new Date(),
        isActive: true
      }],
      stats: {
        totalJobs: 0,
        activeJobs: 0,
        totalApplications: 0,
        totalHires: 0,
        profileViews: 0,
        followerCount: 0
      },
      verification: {
        isVerified: false,
        documents: []
      },
      settings: {
        allowPublicProfile: true,
        showSalaryRanges: true,
        requireCoverLetter: false,
        allowRemoteApplications: true
      },
      awards: [],
      certifications: [],
      specialties: [],
      technologies: [],
      isActive: true,
      isFeatured: false,
      createdBy: user._id
    })

    await newCompany.save()
    return newCompany
  }

  /**
   * Create client profile for job seeker user
   */
  private async createClientForUser(user: any, location: any): Promise<any> {
    const newClient = new Client({
      user: user._id,
      headline: `${user.profile.firstName} ${user.profile.lastName} - Professional`,
      summary: 'I am a motivated professional looking for new opportunities to grow my career.',
      experience: {
        level: 'entry',
        yearsOfExperience: 0,
        industries: []
      },
      jobPreferences: {
        desiredRoles: [],
        industries: [],
        locations: [{
          city: location?.city || '',
          state: location?.state || '',
          country: location?.country || '',
          remote: false,
          relocationWilling: false
        }],
        salaryExpectation: {
          min: 30000,
          max: 60000,
          currency: 'USD',
          period: 'yearly',
          negotiable: true
        },
        jobTypes: ['full-time'],
        workArrangement: ['onsite'],
        availability: 'immediately',
        benefits: [],
        companySize: []
      },
      privacy: {
        profileVisibility: 'public',
        showSalaryExpectation: true,
        showCurrentCompany: true,
        allowRecruiterContact: true,
        showProfileToCurrentEmployer: false
      },
      isActive: true,
      isPublic: true
    })

    await newClient.save()
    return newClient
  }

  /**
   * Format user data for API response
   */
  private async formatAuthResponse(user: any, tokens: { accessToken: string; refreshToken: string }): Promise<AuthResponse> {
    // Get company data if user is company admin
    let companyData: {
      _id: string
      name: string
      slug: string
      description: string
      website?: string
      industry: string[]
      size: string
      verification: any
    } | undefined = undefined

    if (user.role === 'company_admin' && user.companyId) {
      const company = await Company.findById(user.companyId)
      if (company) {
        companyData = {
          _id: company._id.toString(),
          name: company.name,
          slug: company.slug,
          description: company.description,
          website: company.website,
          industry: company.industry,
          size: company.size,
          verification: company.verification
        }
      }
    }

    // Get client data if user is job seeker
    let clientData: {
      _id: string
      headline: string
      summary: string
      experience: any
      profileCompleteness: number
    } | undefined = undefined

    if (user.role === 'job_seeker') {
      const client = await Client.findOne({ user: user._id })
      if (client) {
        clientData = {
          _id: client._id.toString(),
          headline: client.headline,
          summary: client.summary,
          experience: client.experience,
          profileCompleteness: client.activity.profileCompleteness
        }
      }
    }

    return {
      user: {
        id: user._id.toString(),
        email: user.email,
        role: user.role,
        profile: {
          firstName: user.profile.firstName,
          lastName: user.profile.lastName,
          fullName: `${user.profile.firstName} ${user.profile.lastName}`,
          avatar: user.profile.avatar
        },
        isEmailVerified: user.isEmailVerified,
        companyId: user.companyId?.toString()
      },
      tokens,
      company: companyData,
      client: clientData,
      emailVerificationRequired: !user.isEmailVerified
    }
  }
}

export const authService = new AuthService()
