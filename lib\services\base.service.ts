import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'

export interface PaginationOptions {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationResult<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface SearchOptions extends PaginationOptions {
  searchTerm?: string
  filters?: Record<string, any>
}

export abstract class BaseService {
  /**
   * Create pagination metadata
   */
  protected createPaginationMeta(
    page: number,
    limit: number,
    total: number
  ): PaginationResult<any>['pagination'] {
    const pages = Math.ceil(total / limit)
    
    return {
      page,
      limit,
      total,
      pages,
      hasNext: page < pages,
      hasPrev: page > 1
    }
  }

  /**
   * Validate pagination parameters
   */
  protected validatePaginationParams(page?: number, limit?: number): { page: number; limit: number } {
    const validatedPage = Math.max(1, page || 1)
    const validatedLimit = Math.min(100, Math.max(1, limit || 10)) // Max 100 items per page
    
    return {
      page: validatedPage,
      limit: validatedLimit
    }
  }

  /**
   * Build MongoDB sort object
   */
  protected buildSortObject(sortBy?: string, sortOrder?: 'asc' | 'desc'): Record<string, 1 | -1> {
    if (!sortBy) {
      return { createdAt: -1 } // Default sort by creation date, newest first
    }
    
    return {
      [sortBy]: sortOrder === 'asc' ? 1 : -1
    }
  }

  /**
   * Build search query for text fields
   */
  protected buildTextSearchQuery(searchTerm: string, fields: string[]): any {
    if (!searchTerm || !fields.length) {
      return {}
    }
    
    return {
      $or: fields.map(field => ({
        [field]: { $regex: searchTerm, $options: 'i' }
      }))
    }
  }

  /**
   * Validate ObjectId format
   */
  protected validateObjectId(id: string, fieldName: string = 'id'): void {
    const objectIdRegex = /^[0-9a-fA-F]{24}$/
    
    if (!objectIdRegex.test(id)) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Invalid ${fieldName} format`,
        fieldName
      )
    }
  }

  /**
   * Sanitize user input
   */
  protected sanitizeInput(input: any): any {
    if (typeof input === 'string') {
      return input.trim()
    }
    
    if (Array.isArray(input)) {
      return input.map(item => this.sanitizeInput(item))
    }
    
    if (typeof input === 'object' && input !== null) {
      const sanitized: any = {}
      for (const [key, value] of Object.entries(input)) {
        sanitized[key] = this.sanitizeInput(value)
      }
      return sanitized
    }
    
    return input
  }

  /**
   * Check if user has permission for resource
   */
  protected checkResourcePermission(
    resourceOwnerId: string,
    currentUserId: string,
    userRole: string,
    allowedRoles: string[] = ['admin', 'super_admin']
  ): void {
    const isOwner = resourceOwnerId === currentUserId
    const hasAdminRole = allowedRoles.includes(userRole)
    
    if (!isOwner && !hasAdminRole) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'You do not have permission to access this resource',
        'permission'
      )
    }
  }

  /**
   * Format date for API response
   */
  protected formatDate(date: Date): string {
    return date.toISOString()
  }

  /**
   * Generate slug from text
   */
  protected generateSlug(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  /**
   * Validate required fields
   */
  protected validateRequiredFields(data: Record<string, any>, requiredFields: string[]): void {
    const missingFields = requiredFields.filter(field => {
      const value = data[field]
      return value === undefined || value === null || (typeof value === 'string' && value.trim() === '')
    })
    
    if (missingFields.length > 0) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Missing required fields: ${missingFields.join(', ')}`,
        undefined,
        { missingFields }
      )
    }
  }

  /**
   * Create standardized error response
   */
  protected createNotFoundError(resource: string, identifier?: string): never {
    throw errorService.createError(
      ErrorCode.NOT_FOUND,
      `${resource} not found${identifier ? ` with identifier: ${identifier}` : ''}`,
      'resource'
    )
  }

  /**
   * Create standardized duplicate error
   */
  protected createDuplicateError(resource: string, field: string): never {
    throw errorService.createError(
      ErrorCode.DUPLICATE_ENTRY,
      `${resource} with this ${field} already exists`,
      field
    )
  }

  /**
   * Create standardized validation error
   */
  protected createValidationError(message: string, field?: string, details?: any): never {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      message,
      field,
      details
    )
  }

  /**
   * Log service operation
   */
  protected logOperation(operation: string, data?: any): void {
    console.log(`[${this.constructor.name}] ${operation}`, data ? JSON.stringify(data) : '')
  }

  /**
   * Handle database errors
   */
  protected handleDatabaseError(error: any, operation: string): never {
    console.error(`[${this.constructor.name}] Database error in ${operation}:`, error)
    
    // Handle specific MongoDB errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern || {})[0] || 'field'
      this.createDuplicateError('Resource', field)
    }
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message)
      this.createValidationError(`Validation failed: ${validationErrors.join(', ')}`)
    }
    
    if (error.name === 'CastError') {
      this.createValidationError(`Invalid ${error.path}: ${error.value}`, error.path)
    }
    
    // Generic database error
    throw errorService.createError(
      ErrorCode.INTERNAL_SERVER_ERROR,
      'Database operation failed',
      'database'
    )
  }
}

export default BaseService
