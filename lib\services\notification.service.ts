import BaseService from './base.service'
import { Notification } from '@/lib/models/notification.model'
import { User } from '@/lib/models/user.model'
import { cacheService } from './cache.service'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'

export interface NotificationProfile {
  id: string
  type: 'application_status' | 'new_application' | 'job_posted' | 'job_expired' | 'message' | 'system'
  title: string
  message: string
  data?: {
    jobId?: string
    applicationId?: string
    companyId?: string
    url?: string
    [key: string]: any
  }
  isRead: boolean
  readAt?: Date
  priority: 'low' | 'medium' | 'high'
  createdAt: Date
  updatedAt: Date
}

export interface CreateNotificationRequest {
  userId: string
  type: 'application_status' | 'new_application' | 'job_posted' | 'job_expired' | 'message' | 'system'
  title: string
  message: string
  data?: {
    jobId?: string
    applicationId?: string
    companyId?: string
    url?: string
    [key: string]: any
  }
  priority?: 'low' | 'medium' | 'high'
  expiresAt?: Date
}

export interface NotificationStats {
  total: number
  unread: number
  byType: Record<string, number>
  byPriority: Record<string, number>
}

export class NotificationService extends BaseService {
  /**
   * Create a new notification
   */
  async createNotification(notificationData: CreateNotificationRequest): Promise<NotificationProfile> {
    try {
      this.validateRequiredFields(notificationData, ['userId', 'type', 'title', 'message'])
      this.validateObjectId(notificationData.userId, 'userId')

      // Verify user exists
      const user = await User.findById(notificationData.userId)
      if (!user) {
        this.createNotFoundError('User', notificationData.userId)
      }

      // Create notification
      const notification = new Notification({
        ...notificationData,
        priority: notificationData.priority || 'medium'
      })

      await notification.save()

      // Clear user notifications cache
      await this.clearNotificationCaches(notificationData.userId)

      const result = this.formatNotificationProfile(notification)
      this.logOperation('createNotification', { notificationId: result.id, userId: notificationData.userId })

      return result

    } catch (error) {
      this.handleDatabaseError(error, 'createNotification')
    }
  }

  /**
   * Get notifications for a user
   */
  async getUserNotifications(
    userId: string,
    page: number = 1,
    limit: number = 20,
    unreadOnly: boolean = false
  ): Promise<{
    notifications: NotificationProfile[]
    pagination: any
  }> {
    this.validateObjectId(userId, 'userId')
    const { page: validPage, limit: validLimit } = this.validatePaginationParams(page, limit)

    // Try cache first for read notifications
    const cacheKey = `${cacheService.keys.notifications(userId)}:${validPage}:${validLimit}:${unreadOnly}`
    const cached = await cacheService.get(cacheKey)

    if (cached && !unreadOnly) {
      return cached
    }

    const query: any = { userId }
    if (unreadOnly) {
      query.isRead = false
    }

    const skip = (validPage - 1) * validLimit

    const [notifications, total] = await Promise.all([
      Notification.find(query)
        .sort({ priority: -1, createdAt: -1 }) // High priority first, then newest
        .skip(skip)
        .limit(validLimit),
      Notification.countDocuments(query)
    ])

    const result = {
      notifications: notifications.map(notification => this.formatNotificationProfile(notification)),
      pagination: this.createPaginationMeta(validPage, validLimit, total)
    }

    // Cache for 2 minutes if not unread only
    if (!unreadOnly) {
      await cacheService.set(cacheKey, result, 2 * 60)
    }

    return result
  }

  /**
   * Get notification by ID
   */
  async getNotificationById(notificationId: string, userId: string): Promise<NotificationProfile> {
    this.validateObjectId(notificationId, 'notificationId')

    const notification = await Notification.findById(notificationId)

    if (!notification) {
      this.createNotFoundError('Notification', notificationId)
    }

    // Verify user owns this notification
    if (notification.userId.toString() !== userId) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'You do not have permission to view this notification',
        'permission'
      )
    }

    return this.formatNotificationProfile(notification)
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string, userId: string): Promise<void> {
    this.validateObjectId(notificationId, 'notificationId')

    const notification = await Notification.findById(notificationId)

    if (!notification) {
      this.createNotFoundError('Notification', notificationId)
    }

    // Verify user owns this notification
    if (notification.userId.toString() !== userId) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'You do not have permission to modify this notification',
        'permission'
      )
    }

    if (!notification.isRead) {
      notification.isRead = true
      notification.readAt = new Date()
      await notification.save()

      // Clear caches
      await this.clearNotificationCaches(userId)
    }

    this.logOperation('markNotificationAsRead', { notificationId, userId })
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: string): Promise<void> {
    this.validateObjectId(userId, 'userId')

    await Notification.updateMany(
      { userId, isRead: false },
      { isRead: true, readAt: new Date() }
    )

    // Clear caches
    await this.clearNotificationCaches(userId)

    this.logOperation('markAllNotificationsAsRead', { userId })
  }

  /**
   * Delete notification
   */
  async deleteNotification(notificationId: string, userId: string): Promise<void> {
    this.validateObjectId(notificationId, 'notificationId')

    const notification = await Notification.findById(notificationId)

    if (!notification) {
      this.createNotFoundError('Notification', notificationId)
    }

    // Verify user owns this notification
    if (notification.userId.toString() !== userId) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'You do not have permission to delete this notification',
        'permission'
      )
    }

    await Notification.findByIdAndDelete(notificationId)

    // Clear caches
    await this.clearNotificationCaches(userId)

    this.logOperation('deleteNotification', { notificationId, userId })
  }

  /**
   * Get notification statistics for a user
   */
  async getNotificationStats(userId: string): Promise<NotificationStats> {
    this.validateObjectId(userId, 'userId')

    // Try cache first
    const cacheKey = `${cacheService.keys.notifications(userId)}:stats`
    const cached = await cacheService.get<NotificationStats>(cacheKey)

    if (cached) {
      return cached
    }

    const [total, unread, byType, byPriority] = await Promise.all([
      Notification.countDocuments({ userId }),
      Notification.countDocuments({ userId, isRead: false }),
      Notification.aggregate([
        { $match: { userId: userId } },
        { $group: { _id: '$type', count: { $sum: 1 } } }
      ]),
      Notification.aggregate([
        { $match: { userId: userId, isRead: false } },
        { $group: { _id: '$priority', count: { $sum: 1 } } }
      ])
    ])

    const stats: NotificationStats = {
      total,
      unread,
      byType: byType.reduce((acc, item) => {
        acc[item._id] = item.count
        return acc
      }, {} as Record<string, number>),
      byPriority: byPriority.reduce((acc, item) => {
        acc[item._id] = item.count
        return acc
      }, {} as Record<string, number>)
    }

    // Cache for 5 minutes
    await cacheService.set(cacheKey, stats, 5 * 60)

    return stats
  }

  /**
   * Create application status notification
   */
  async createApplicationStatusNotification(
    userId: string,
    applicationId: string,
    jobTitle: string,
    newStatus: string
  ): Promise<void> {
    const statusMessages = {
      reviewing: 'Your application is now being reviewed',
      shortlisted: 'Congratulations! You have been shortlisted',
      interviewed: 'You have been selected for an interview',
      offered: 'Congratulations! You have received a job offer',
      hired: 'Congratulations! You have been hired',
      rejected: 'Unfortunately, your application was not successful'
    }

    const message = statusMessages[newStatus as keyof typeof statusMessages] || `Your application status has been updated to ${newStatus}`

    await this.createNotification({
      userId,
      type: 'application_status',
      title: `Application Update: ${jobTitle}`,
      message,
      data: {
        applicationId,
        url: `/applications/${applicationId}`
      },
      priority: ['offered', 'hired', 'interviewed'].includes(newStatus) ? 'high' : 'medium'
    })
  }

  /**
   * Create new application notification for company
   */
  async createNewApplicationNotification(
    companyAdminId: string,
    jobTitle: string,
    applicantName: string,
    applicationId: string
  ): Promise<void> {
    await this.createNotification({
      userId: companyAdminId,
      type: 'new_application',
      title: 'New Job Application',
      message: `${applicantName} has applied for ${jobTitle}`,
      data: {
        applicationId,
        url: `/applications/${applicationId}`
      },
      priority: 'medium'
    })
  }

  /**
   * Format notification data for API response
   */
  private formatNotificationProfile(notification: any): NotificationProfile {
    return {
      id: notification._id.toString(),
      type: notification.type,
      title: notification.title,
      message: notification.message,
      data: notification.data,
      isRead: notification.isRead,
      readAt: notification.readAt,
      priority: notification.priority,
      createdAt: notification.createdAt,
      updatedAt: notification.updatedAt
    }
  }

  /**
   * Clear notification-related caches
   */
  private async clearNotificationCaches(userId: string): Promise<void> {
    const pattern = `${cacheService.keys.notifications(userId)}*`
    const keys = await cacheService.keys(pattern)
    
    for (const key of keys) {
      await cacheService.del(key)
    }
  }

  /**
   * Clean up expired notifications (should be called periodically)
   */
  async cleanupExpiredNotifications(): Promise<number> {
    const result = await Notification.deleteMany({
      expiresAt: { $lt: new Date() }
    })

    this.logOperation('cleanupExpiredNotifications', { deletedCount: result.deletedCount })
    return result.deletedCount || 0
  }
}

export const notificationService = new NotificationService()
