import { User } from '@/lib/models/user.model'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'

export interface UserProfile {
  id: string
  email: string
  role: string
  profile: {
    firstName: string
    lastName: string
    fullName: string
    avatar?: string
    phone?: string
    location?: {
      city?: string
      state?: string
      country?: string
    }
  }
  preferences: {
    emailNotifications: boolean
    jobAlerts: boolean
    marketingEmails: boolean
    theme: string
  }
  isEmailVerified: boolean
  isActive: boolean
  companyId?: string
  createdAt: Date
  updatedAt: Date
  lastLogin?: Date
}

export interface UpdateUserRequest {
  firstName?: string
  lastName?: string
  phone?: string
  location?: {
    city?: string
    state?: string
    country?: string
  }
  preferences?: {
    emailNotifications?: boolean
    jobAlerts?: boolean
    marketingEmails?: boolean
    theme?: string
  }
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export class UserService {
  /**
   * Get user profile by ID
   */
  async getUserById(userId: string): Promise<UserProfile> {
    const user = await User.findById(userId).populate('companyId', 'name slug')
    
    if (!user) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'User not found',
        'userId'
      )
    }
    
    if (!user.isActive) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'Account has been deactivated',
        'account'
      )
    }
    
    return this.formatUserProfile(user)
  }

  /**
   * Get user profile by email
   */
  async getUserByEmail(email: string): Promise<UserProfile> {
    const user = await User.findOne({ email: email.toLowerCase() }).populate('companyId', 'name slug')
    
    if (!user) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'User not found',
        'email'
      )
    }
    
    return this.formatUserProfile(user)
  }

  /**
   * Update user profile
   */
  async updateUser(userId: string, updateData: UpdateUserRequest): Promise<UserProfile> {
    const user = await User.findById(userId)
    
    if (!user) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'User not found',
        'userId'
      )
    }
    
    // Update profile fields
    if (updateData.firstName) user.profile.firstName = updateData.firstName
    if (updateData.lastName) user.profile.lastName = updateData.lastName
    if (updateData.phone) user.profile.phone = updateData.phone
    if (updateData.location) user.profile.location = { ...user.profile.location, ...updateData.location }
    
    // Update preferences
    if (updateData.preferences) {
      user.preferences = { ...user.preferences, ...updateData.preferences }
    }
    
    await user.save()
    
    return this.formatUserProfile(user)
  }

  /**
   * Change user password
   */
  async changePassword(userId: string, passwordData: ChangePasswordRequest): Promise<void> {
    const user = await User.findById(userId).select('+password')
    
    if (!user) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'User not found',
        'userId'
      )
    }
    
    // Verify current password
    const isCurrentPasswordValid = await user.comparePassword(passwordData.currentPassword)
    if (!isCurrentPasswordValid) {
      throw errorService.createError(
        ErrorCode.INVALID_CREDENTIALS,
        'Current password is incorrect',
        'currentPassword'
      )
    }
    
    // Update password
    user.password = passwordData.newPassword
    await user.save()
  }

  /**
   * Deactivate user account
   */
  async deactivateUser(userId: string): Promise<void> {
    const user = await User.findById(userId)
    
    if (!user) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'User not found',
        'userId'
      )
    }
    
    user.isActive = false
    await user.save()
  }

  /**
   * Verify user email
   */
  async verifyEmail(userId: string): Promise<void> {
    const user = await User.findById(userId)
    
    if (!user) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'User not found',
        'userId'
      )
    }
    
    user.isEmailVerified = true
    await user.save()
  }

  /**
   * Get users with pagination
   */
  async getUsers(page: number = 1, limit: number = 10, filters?: { role?: string; isActive?: boolean }): Promise<{
    users: UserProfile[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    }
  }> {
    const query: any = {}
    
    if (filters?.role) query.role = filters.role
    if (filters?.isActive !== undefined) query.isActive = filters.isActive
    
    const skip = (page - 1) * limit
    
    const [users, total] = await Promise.all([
      User.find(query)
        .populate('companyId', 'name slug')
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }),
      User.countDocuments(query)
    ])
    
    return {
      users: users.map(user => this.formatUserProfile(user)),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * Format user data for API response
   */
  private formatUserProfile(user: any): UserProfile {
    return {
      id: user._id.toString(),
      email: user.email,
      role: user.role,
      profile: {
        firstName: user.profile.firstName,
        lastName: user.profile.lastName,
        fullName: `${user.profile.firstName} ${user.profile.lastName}`,
        avatar: user.profile.avatar,
        phone: user.profile.phone,
        location: user.profile.location
      },
      preferences: user.preferences,
      isEmailVerified: user.isEmailVerified,
      isActive: user.isActive,
      companyId: user.companyId?.toString(),
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLogin: user.lastLogin
    }
  }
}

export const userService = new UserService()
