import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface Company {
  _id: string
  name: string
  slug: string
  description: string
  tagline?: string
  logo?: string
  coverImage?: string
  website?: string
  industry: string[]
  size: string
  founded?: number
  locations: Array<{
    city: string
    state?: string
    country: string
    isHeadquarters: boolean
    address?: string
    postalCode?: string
  }>
  contact: {
    email: string
    phone?: string
    address?: string
    supportEmail?: string
    hrEmail?: string
  }
  culture: {
    values?: string[]
    benefits?: string[]
    workEnvironment?: string
    diversity?: string
    mission?: string
    vision?: string
    perks?: string[]
  }
  socialLinks: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
    github?: string
    youtube?: string
    glassdoor?: string
  }
  subscription: {
    plan: string
    status: string
    jobPostingLimit: number
    jobPostingsUsed: number
    featuredJobsLimit: number
    featuredJobsUsed: number
  }
  stats: {
    totalJobs: number
    activeJobs: number
    totalApplications: number
    totalHires: number
    profileViews: number
    followerCount: number
  }
  verification: {
    isVerified: boolean
    verifiedAt?: Date
    documents?: string[]
  }
  settings: {
    allowPublicProfile: boolean
    showSalaryRanges: boolean
    requireCoverLetter: boolean
    allowRemoteApplications: boolean
  }
  isActive: boolean
  isFeatured: boolean
  createdAt: Date
  updatedAt: Date
}

interface CompanyState {
  // Current company data
  company: Company | null
  
  // Loading states
  loading: boolean
  profileLoading: boolean
  updateLoading: boolean
  
  // Error state
  error: string | null
  
  // Team members and jobs
  teamMembers: any[]
  jobs: any[]
  applications: any[]
  
  // Analytics data
  analytics: {
    profileViews: number[]
    applicationTrends: number[]
    jobPerformance: any[]
  }
}

interface CompanyActions {
  // Company profile management
  fetchCompanyProfile: () => Promise<void>
  updateCompanyProfile: (data: Partial<Company>) => Promise<void>
  
  // Team management
  fetchTeamMembers: () => Promise<void>
  inviteTeamMember: (email: string, role: string) => Promise<void>
  removeTeamMember: (memberId: string) => Promise<void>
  
  // Job management
  fetchCompanyJobs: () => Promise<void>
  createJob: (jobData: any) => Promise<void>
  updateJob: (jobId: string, jobData: any) => Promise<void>
  deleteJob: (jobId: string) => Promise<void>
  
  // Application management
  fetchApplications: () => Promise<void>
  updateApplicationStatus: (applicationId: string, status: string) => Promise<void>
  
  // Analytics
  fetchAnalytics: () => Promise<void>
  
  // Utility
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
  reset: () => void
}

type CompanyStore = CompanyState & CompanyActions

const initialState: CompanyState = {
  company: null,
  loading: false,
  profileLoading: false,
  updateLoading: false,
  error: null,
  teamMembers: [],
  jobs: [],
  applications: [],
  analytics: {
    profileViews: [],
    applicationTrends: [],
    jobPerformance: []
  }
}

export const useCompanyStore = create<CompanyStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Company profile management
      fetchCompanyProfile: async () => {
        set({ profileLoading: true, error: null })
        
        try {
          const response = await fetch('/api/v1/companies/me', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch company profile')
          }

          const data = await response.json()
          
          set({ 
            company: data.data,
            profileLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch company profile',
            profileLoading: false 
          })
        }
      },

      updateCompanyProfile: async (data: Partial<Company>) => {
        set({ updateLoading: true, error: null })
        
        try {
          const response = await fetch('/api/v1/companies/me', {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            throw new Error('Failed to update company profile')
          }

          const result = await response.json()
          
          set({ 
            company: result.data,
            updateLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update company profile',
            updateLoading: false 
          })
        }
      },

      // Team management
      fetchTeamMembers: async () => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/team`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch team members')
          }

          const data = await response.json()
          
          set({ teamMembers: data.data })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch team members'
          })
        }
      },

      inviteTeamMember: async (email: string, role: string) => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/team/invite`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, role })
          })

          if (!response.ok) {
            throw new Error('Failed to invite team member')
          }

          // Refresh team members
          await get().fetchTeamMembers()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to invite team member'
          })
        }
      },

      removeTeamMember: async (memberId: string) => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/team/${memberId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to remove team member')
          }

          // Refresh team members
          await get().fetchTeamMembers()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to remove team member'
          })
        }
      },

      // Job management
      fetchCompanyJobs: async () => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/jobs`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch company jobs')
          }

          const data = await response.json()
          
          set({ jobs: data.data })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch company jobs'
          })
        }
      },

      createJob: async (jobData: any) => {
        try {
          const response = await fetch('/api/v1/jobs', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(jobData)
          })

          if (!response.ok) {
            throw new Error('Failed to create job')
          }

          // Refresh jobs
          await get().fetchCompanyJobs()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to create job'
          })
        }
      },

      updateJob: async (jobId: string, jobData: any) => {
        try {
          const response = await fetch(`/api/v1/jobs/${jobId}`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(jobData)
          })

          if (!response.ok) {
            throw new Error('Failed to update job')
          }

          // Refresh jobs
          await get().fetchCompanyJobs()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update job'
          })
        }
      },

      deleteJob: async (jobId: string) => {
        try {
          const response = await fetch(`/api/v1/jobs/${jobId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to delete job')
          }

          // Refresh jobs
          await get().fetchCompanyJobs()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to delete job'
          })
        }
      },

      // Application management
      fetchApplications: async () => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/applications`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch applications')
          }

          const data = await response.json()
          
          set({ applications: data.data })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch applications'
          })
        }
      },

      updateApplicationStatus: async (applicationId: string, status: string) => {
        try {
          const response = await fetch(`/api/v1/applications/${applicationId}/status`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status })
          })

          if (!response.ok) {
            throw new Error('Failed to update application status')
          }

          // Refresh applications
          await get().fetchApplications()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update application status'
          })
        }
      },

      // Analytics
      fetchAnalytics: async () => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/analytics`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch analytics')
          }

          const data = await response.json()
          
          set({ analytics: data.data })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch analytics'
          })
        }
      },

      // Utility
      setLoading: (loading: boolean) => set({ loading }),
      
      setError: (error: string | null) => set({ error }),
      
      clearError: () => set({ error: null }),
      
      reset: () => set(initialState)
    }),
    {
      name: 'company-store',
      partialize: (state) => ({
        company: state.company
      })
    }
  )
)
