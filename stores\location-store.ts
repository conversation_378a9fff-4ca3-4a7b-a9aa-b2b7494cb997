import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { locationService, type LocationData, type LocationHierarchy } from '@/lib/location-service'

export interface JobVisibilityPreferences {
  jobTypes: string[]
  experienceLevels: string[]
  workModels: string[]
  salaryRange: {
    min: number
    max: number
  }
  locations: {
    includeLocal: boolean
    includeRegional: boolean
    includeNational: boolean
    includeContinental: boolean
    includeInternational: boolean
    maxDistance?: number // in kilometers
    specificLocations: string[]
  }
  industries: string[]
  companyTypes: string[]
  remoteOnly: boolean
}

export interface LocationState {
  // Location data
  currentLocation: LocationData | null
  locationHierarchy: LocationHierarchy | null
  isLocationLoading: boolean
  locationError: string | null
  hasLocationPermission: boolean | null
  showLocationBanner: boolean
  
  // Job visibility preferences
  jobPreferences: JobVisibilityPreferences
  
  // Actions
  requestLocation: () => Promise<void>
  setLocationPermission: (granted: boolean) => void
  dismissLocationBanner: () => void
  clearLocation: () => void
  updateJobPreferences: (preferences: Partial<JobVisibilityPreferences>) => void
  resetJobPreferences: () => void
}

const defaultJobPreferences: JobVisibilityPreferences = {
  jobTypes: ['Full-time', 'Part-time', 'Contract', 'Freelance'],
  experienceLevels: ['Entry Level', 'Mid Level', 'Senior Level', 'Executive'],
  workModels: ['On-site', 'Remote', 'Hybrid'],
  salaryRange: {
    min: 0,
    max: 1000000
  },
  locations: {
    includeLocal: true,
    includeRegional: true,
    includeNational: true,
    includeContinental: false,
    includeInternational: false,
    maxDistance: 50, // 50km radius
    specificLocations: []
  },
  industries: [],
  companyTypes: [],
  remoteOnly: false
}

export const useLocationStore = create<LocationState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentLocation: null,
      locationHierarchy: null,
      isLocationLoading: false,
      locationError: null,
      hasLocationPermission: null,
      showLocationBanner: true,
      jobPreferences: defaultJobPreferences,

      // Actions
      requestLocation: async () => {
        set({ isLocationLoading: true, locationError: null })
        
        try {
          const result = await locationService.getCurrentLocation()
          
          if (result.success && result.location) {
            const hierarchy = locationService.getLocationHierarchy(result.location)
            
            set({
              currentLocation: result.location,
              locationHierarchy: hierarchy,
              isLocationLoading: false,
              hasLocationPermission: true,
              showLocationBanner: false,
              locationError: null
            })

            // Auto-update job preferences based on location
            const currentPrefs = get().jobPreferences
            set({
              jobPreferences: {
                ...currentPrefs,
                locations: {
                  ...currentPrefs.locations,
                  specificLocations: [
                    result.location.city,
                    result.location.region,
                    result.location.country
                  ].filter(Boolean)
                }
              }
            })
          } else {
            set({
              isLocationLoading: false,
              locationError: result.error || 'Failed to get location',
              hasLocationPermission: false
            })
          }
        } catch (error) {
          set({
            isLocationLoading: false,
            locationError: error instanceof Error ? error.message : 'Location request failed',
            hasLocationPermission: false
          })
        }
      },

      setLocationPermission: (granted: boolean) => {
        set({ 
          hasLocationPermission: granted,
          showLocationBanner: !granted
        })
        
        if (granted) {
          get().requestLocation()
        }
      },

      dismissLocationBanner: () => {
        set({ 
          showLocationBanner: false,
          hasLocationPermission: false
        })
      },

      clearLocation: () => {
        locationService.clearLocationCache()
        set({
          currentLocation: null,
          locationHierarchy: null,
          hasLocationPermission: null,
          showLocationBanner: true,
          locationError: null
        })
      },

      updateJobPreferences: (preferences: Partial<JobVisibilityPreferences>) => {
        set({
          jobPreferences: {
            ...get().jobPreferences,
            ...preferences
          }
        })
      },

      resetJobPreferences: () => {
        set({ jobPreferences: defaultJobPreferences })
      }
    }),
    {
      name: 'location-store',
      partialize: (state) => ({
        currentLocation: state.currentLocation,
        locationHierarchy: state.locationHierarchy,
        hasLocationPermission: state.hasLocationPermission,
        showLocationBanner: state.showLocationBanner,
        jobPreferences: state.jobPreferences
      })
    }
  )
)

// Helper functions for job filtering based on location and preferences
export const getLocationBasedJobs = (
  jobs: any[],
  currentLocation: LocationData | null,
  preferences: JobVisibilityPreferences
) => {
  if (!jobs.length) return []

  let filteredJobs = jobs

  // Filter by job types
  if (preferences.jobTypes.length > 0) {
    filteredJobs = filteredJobs.filter(job => 
      preferences.jobTypes.includes(job.type)
    )
  }

  // Filter by experience levels
  if (preferences.experienceLevels.length > 0) {
    filteredJobs = filteredJobs.filter(job => 
      preferences.experienceLevels.includes(job.experience)
    )
  }

  // Filter by work models
  if (preferences.workModels.length > 0) {
    filteredJobs = filteredJobs.filter(job => 
      preferences.workModels.includes(job.workModel)
    )
  }

  // Filter by remote only
  if (preferences.remoteOnly) {
    filteredJobs = filteredJobs.filter(job => 
      job.workModel === 'Remote' || job.remote === true
    )
  }

  // Filter by salary range
  filteredJobs = filteredJobs.filter(job => {
    if (!job.salary || !job.salary.min) return true
    return job.salary.min >= preferences.salaryRange.min && 
           job.salary.min <= preferences.salaryRange.max
  })

  // Location-based filtering
  if (currentLocation && !preferences.remoteOnly) {
    const locationFilters = preferences.locations
    
    filteredJobs = filteredJobs.filter(job => {
      // Always include remote jobs
      if (job.workModel === 'Remote' || job.remote === true) {
        return true
      }

      const jobLocation = job.location.toLowerCase()
      const userCity = currentLocation.city.toLowerCase()
      const userRegion = currentLocation.region.toLowerCase()
      const userCountry = currentLocation.country.toLowerCase()
      const userContinent = currentLocation.continent.toLowerCase()

      // Check specific locations
      if (locationFilters.specificLocations.length > 0) {
        const matchesSpecific = locationFilters.specificLocations.some(loc =>
          jobLocation.includes(loc.toLowerCase())
        )
        if (matchesSpecific) return true
      }

      // Check hierarchical location preferences
      if (locationFilters.includeLocal && jobLocation.includes(userCity)) {
        return true
      }

      if (locationFilters.includeRegional && jobLocation.includes(userRegion)) {
        return true
      }

      if (locationFilters.includeNational && jobLocation.includes(userCountry)) {
        return true
      }

      if (locationFilters.includeContinental && jobLocation.includes(userContinent)) {
        return true
      }

      if (locationFilters.includeInternational) {
        return true
      }

      return false
    })
  }

  // Sort by relevance (location proximity, then other factors)
  if (currentLocation) {
    filteredJobs.sort((a, b) => {
      // Prioritize remote jobs for remote-friendly users
      if (preferences.remoteOnly) {
        if (a.workModel === 'Remote' && b.workModel !== 'Remote') return -1
        if (b.workModel === 'Remote' && a.workModel !== 'Remote') return 1
      }

      // Prioritize local jobs
      const aLocal = a.location.toLowerCase().includes(currentLocation.city.toLowerCase())
      const bLocal = b.location.toLowerCase().includes(currentLocation.city.toLowerCase())
      
      if (aLocal && !bLocal) return -1
      if (bLocal && !aLocal) return 1

      // Then regional
      const aRegional = a.location.toLowerCase().includes(currentLocation.region.toLowerCase())
      const bRegional = b.location.toLowerCase().includes(currentLocation.region.toLowerCase())
      
      if (aRegional && !bRegional) return -1
      if (bRegional && !aRegional) return 1

      // Then national
      const aNational = a.location.toLowerCase().includes(currentLocation.country.toLowerCase())
      const bNational = b.location.toLowerCase().includes(currentLocation.country.toLowerCase())
      
      if (aNational && !bNational) return -1
      if (bNational && !aNational) return 1

      // Finally by posted date (newest first)
      return new Date(b.posted).getTime() - new Date(a.posted).getTime()
    })
  }

  return filteredJobs
}
