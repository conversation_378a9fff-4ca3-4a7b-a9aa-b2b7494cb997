import { BaseDocument, ObjectId } from './base.types'

export type PaymentStatus = 'pending' | 'processing' | 'succeeded' | 'failed' | 'cancelled' | 'refunded'

export type PaymentMethodType = 'card' | 'bank_transfer' | 'paypal' | 'stripe' | 'apple_pay' | 'google_pay'

export type SubscriptionStatus = 'active' | 'inactive' | 'cancelled' | 'past_due' | 'unpaid' | 'trialing'

export type InvoiceStatus = 'draft' | 'open' | 'paid' | 'void' | 'uncollectible'

export type RefundStatus = 'pending' | 'succeeded' | 'failed' | 'cancelled'

export interface PaymentPlan {
  id: string
  name: string
  description: string
  type: 'job_seeker' | 'company'
  tier: 'free' | 'basic' | 'premium' | 'enterprise'
  price: {
    amount: number
    currency: string
    interval: 'month' | 'year'
    intervalCount: number
  }
  features: {
    jobPostings?: number
    featuredJobs?: number
    candidateSearch?: number
    teamMembers?: number
    analytics: boolean
    prioritySupport: boolean
    customBranding: boolean
    apiAccess: boolean
    bulkOperations: boolean
    advancedFilters: boolean
    aiRecommendations: boolean
    videoInterviews: boolean
    backgroundChecks: boolean
  }
  limits: {
    jobApplications?: number
    savedJobs?: number
    jobAlerts?: number
    profileViews?: number
    messagesSent?: number
    resumeDownloads?: number
  }
  stripePriceId?: string
  isActive: boolean
  isPopular: boolean
  trialDays?: number
  setupFee?: number
}

export interface Subscription extends BaseDocument {
  user: ObjectId
  company?: ObjectId
  plan: PaymentPlan
  status: SubscriptionStatus
  currentPeriodStart: Date
  currentPeriodEnd: Date
  cancelAtPeriodEnd: boolean
  canceledAt?: Date
  cancelReason?: string
  trialStart?: Date
  trialEnd?: Date
  stripeSubscriptionId?: string
  stripeCustomerId?: string
  usage: {
    jobPostings: number
    featuredJobs: number
    candidateSearch: number
    teamMembers: number
    jobApplications: number
    savedJobs: number
    jobAlerts: number
    profileViews: number
    messagesSent: number
    resumeDownloads: number
  }
  metadata?: Record<string, any>
}

export interface Payment extends BaseDocument {
  user: ObjectId
  company?: ObjectId
  subscription?: ObjectId
  amount: number
  currency: string
  status: PaymentStatus
  paymentMethod: PaymentMethodType
  description: string
  invoice?: ObjectId
  stripePaymentIntentId?: string
  stripeChargeId?: string
  failureReason?: string
  refundedAmount?: number
  refundedAt?: Date
  metadata?: Record<string, any>
}

export interface Invoice extends BaseDocument {
  user: ObjectId
  company?: ObjectId
  subscription?: ObjectId
  number: string
  status: InvoiceStatus
  amount: number
  currency: string
  tax?: number
  discount?: number
  total: number
  dueDate: Date
  paidAt?: Date
  voidedAt?: Date
  items: {
    description: string
    quantity: number
    unitPrice: number
    amount: number
    period?: {
      start: Date
      end: Date
    }
  }[]
  stripeInvoiceId?: string
  downloadUrl?: string
  metadata?: Record<string, any>
}

export interface PaymentMethod extends BaseDocument {
  user: ObjectId
  type: PaymentMethodType
  isDefault: boolean
  card?: {
    brand: string
    last4: string
    expMonth: number
    expYear: number
    country?: string
  }
  bankAccount?: {
    bankName: string
    accountType: string
    last4: string
    country: string
  }
  stripePaymentMethodId?: string
  metadata?: Record<string, any>
}

export interface Refund extends BaseDocument {
  payment: ObjectId
  amount: number
  currency: string
  reason: string
  status: RefundStatus
  stripeRefundId?: string
  processedAt?: Date
  failureReason?: string
  metadata?: Record<string, any>
}

export interface BillingAddress {
  name: string
  company?: string
  line1: string
  line2?: string
  city: string
  state?: string
  postalCode: string
  country: string
}

export interface TaxInfo {
  taxId?: string
  taxIdType?: 'vat' | 'gst' | 'ein' | 'ssn'
  taxExempt: boolean
  taxRate?: number
}

export interface Customer extends BaseDocument {
  user: ObjectId
  company?: ObjectId
  stripeCustomerId?: string
  email: string
  name: string
  phone?: string
  billingAddress?: BillingAddress
  taxInfo?: TaxInfo
  defaultPaymentMethod?: ObjectId
  balance: number
  currency: string
  metadata?: Record<string, any>
}

export interface CreateSubscriptionRequest {
  planId: string
  paymentMethodId?: string
  couponCode?: string
  billingAddress?: BillingAddress
  taxInfo?: TaxInfo
}

export interface UpdateSubscriptionRequest {
  planId?: string
  cancelAtPeriodEnd?: boolean
  couponCode?: string
}

export interface CreatePaymentRequest {
  amount: number
  currency: string
  paymentMethodId: string
  description: string
  metadata?: Record<string, any>
}

export interface PaymentIntent {
  id: string
  amount: number
  currency: string
  status: PaymentStatus
  clientSecret: string
  paymentMethod?: PaymentMethod
  metadata?: Record<string, any>
}

export interface Coupon {
  id: string
  name: string
  code: string
  type: 'percentage' | 'fixed_amount'
  value: number
  currency?: string
  duration: 'once' | 'repeating' | 'forever'
  durationInMonths?: number
  maxRedemptions?: number
  redemptions: number
  validFrom: Date
  validUntil?: Date
  isActive: boolean
  applicablePlans?: string[]
  minimumAmount?: number
  firstTimeOnly: boolean
  metadata?: Record<string, any>
}

export interface UsageRecord {
  id: ObjectId
  subscription: ObjectId
  feature: string
  quantity: number
  timestamp: Date
  metadata?: Record<string, any>
}

export interface BillingHistory {
  subscriptions: Subscription[]
  payments: Payment[]
  invoices: Invoice[]
  refunds: Refund[]
  usageRecords: UsageRecord[]
}

export interface PaymentAnalytics {
  period: 'day' | 'week' | 'month' | 'quarter' | 'year'
  startDate: Date
  endDate: Date
  metrics: {
    totalRevenue: number
    recurringRevenue: number
    oneTimeRevenue: number
    refunds: number
    netRevenue: number
    newSubscriptions: number
    canceledSubscriptions: number
    churnRate: number
    averageRevenuePerUser: number
    lifetimeValue: number
    conversionRate: number
    paymentFailures: number
    successfulPayments: number
  }
  breakdown: {
    byPlan: { plan: string; revenue: number; subscribers: number }[]
    byPaymentMethod: { method: PaymentMethod; revenue: number; count: number }[]
    byCurrency: { currency: string; revenue: number }[]
    byCountry: { country: string; revenue: number; subscribers: number }[]
  }
}

export interface WebhookEvent {
  id: string
  type: string
  data: any
  created: Date
  processed: boolean
  processedAt?: Date
  error?: string
  retryCount: number
  metadata?: Record<string, any>
}
