import { BaseDocument, Location, SocialLinks, FileUpload, ObjectId } from './base.types'

export type UserRole = 'admin' | 'company_admin' | 'recruiter' | 'job_seeker'

export type SubscriptionPlan = 'free' | 'premium' | 'enterprise'

export type SubscriptionStatus = 'active' | 'inactive' | 'cancelled' | 'trial'

export interface UserProfile {
  firstName: string
  lastName: string
  avatar?: string
  phone?: string
  location: Location
  bio?: string
  headline?: string
  dateOfBirth?: Date
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
}

export interface UserPreferences {
  emailNotifications: boolean
  jobAlerts: boolean
  marketingEmails: boolean
  theme: 'light' | 'dark' | 'system'
  language: string
  timezone: string
  currency: string
}

export interface UserSubscription {
  plan: SubscriptionPlan
  status: SubscriptionStatus
  stripeCustomerId?: string
  stripeSubscriptionId?: string
  currentPeriodStart?: Date
  currentPeriodEnd?: Date
  cancelAtPeriodEnd?: boolean
  trialEnd?: Date
}

export interface UserStats {
  profileViews: number
  jobApplications: number
  savedJobs: number
  profileCompleteness: number
  lastActive: Date
}

export interface User extends BaseDocument {
  email: string
  password?: string // Optional for responses
  role: UserRole
  profile: UserProfile
  preferences: UserPreferences
  subscription?: UserSubscription
  stats?: UserStats
  companyId?: ObjectId
  isActive: boolean
  isEmailVerified: boolean
  emailVerificationToken?: string
  passwordResetToken?: string
  passwordResetExpires?: Date
  lastLogin?: Date
  twoFactorEnabled?: boolean
  twoFactorSecret?: string
}

export interface CreateUserRequest {
  email: string
  password: string
  firstName: string
  lastName: string
  role?: UserRole
  phone?: string
  location?: Partial<Location>
}

export interface UpdateUserRequest {
  profile?: Partial<UserProfile>
  preferences?: Partial<UserPreferences>
  password?: string
  currentPassword?: string
}

export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
  twoFactorCode?: string
}

export interface LoginResponse {
  user: Omit<User, 'password'>
  tokens: {
    accessToken: string
    refreshToken: string
  }
}

export interface RegisterRequest extends CreateUserRequest {
  confirmPassword: string
  acceptTerms: boolean
}

export interface RegisterResponse extends LoginResponse {
  emailVerificationRequired: boolean
}

export interface ForgotPasswordRequest {
  email: string
}

export interface ResetPasswordRequest {
  token: string
  password: string
  confirmPassword: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface VerifyEmailRequest {
  token: string
}

export interface RefreshTokenRequest {
  refreshToken: string
}

export interface UserSearchQuery {
  q?: string
  role?: UserRole
  location?: string
  isActive?: boolean
  isEmailVerified?: boolean
  companyId?: ObjectId
  page?: number
  limit?: number
  sort?: 'createdAt' | 'lastLogin' | 'email' | 'profile.firstName'
  order?: 'asc' | 'desc'
}

export interface UserListResponse {
  users: Omit<User, 'password'>[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Job Seeker specific types
export interface WorkExperience {
  id: string
  title: string
  company: string
  location: Location
  startDate: Date
  endDate?: Date
  current: boolean
  description: string
  skills: string[]
  achievements?: string[]
}

export interface Education {
  id: string
  institution: string
  degree: string
  fieldOfStudy: string
  startDate: Date
  endDate?: Date
  current: boolean
  gpa?: number
  description?: string
  location: Location
}

export interface Certification {
  id: string
  name: string
  issuer: string
  issueDate: Date
  expiryDate?: Date
  credentialId?: string
  credentialUrl?: string
  description?: string
}

export interface PortfolioItem {
  id: string
  title: string
  description: string
  url?: string
  images?: FileUpload[]
  technologies: string[]
  category: string
  featured: boolean
  completedDate: Date
}

export interface JobPreferences {
  desiredRoles: string[]
  salaryRange: {
    min: number
    max: number
    currency: string
  }
  locations: Location[]
  remoteWork: boolean
  employmentTypes: ('full-time' | 'part-time' | 'contract' | 'freelance' | 'internship')[]
  industries: string[]
  companySize: ('startup' | 'small' | 'medium' | 'large' | 'enterprise')[]
  benefits: string[]
  workEnvironment: ('office' | 'remote' | 'hybrid')[]
}

export interface CandidateProfile extends BaseDocument {
  user: ObjectId
  headline: string
  summary: string
  experience: WorkExperience[]
  education: Education[]
  skills: string[]
  certifications: Certification[]
  portfolio: PortfolioItem[]
  resume: FileUpload[]
  preferences: JobPreferences
  availability: 'immediately' | '2_weeks' | '1_month' | '3_months' | 'not_looking'
  salaryExpectation: {
    min: number
    max: number
    currency: string
    negotiable: boolean
  }
  socialLinks: SocialLinks
  languages: {
    language: string
    proficiency: 'basic' | 'conversational' | 'fluent' | 'native'
  }[]
  isPublic: boolean
  profileViews: number
  lastUpdated: Date
}
